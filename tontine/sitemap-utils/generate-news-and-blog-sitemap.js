const strings = require('../data-resource/strings.json')
const { getDaysDifference, logCommonError } = require('./SitemapUtilFunctions')

/**
 * Generates news sitemap based on the provided original path parameters and location path.
 *
 * - `originalPathParams` - The original path parameters for the sitemap.
 * - `locPath` - The location path for the sitemap.
 *
 * This function first finds the matching slug from the news data.
 * It then calculates the days difference from the updated date of the found slug.
 * If the days difference is less than or equal to 2, it returns an object containing the news details and the original path parameters.
 * If the days difference is more than 2, it returns the original path parameters.
 *
 * **The 2 day period is in accordance to Google's news sitemap docs**.
 *
 * Returns an object containing the modified sitemap entry properties.
 * If an error occurs during the process, it logs the error message and returns the original path parameters.
 */
function generateNewsAndBlogSitemap(originalPathParams, locPath, newsData) {
  try {
    const foundSlug = newsData?.find((item) => locPath === item?.slug)
    const daysDifference = foundSlug
      ? getDaysDifference(foundSlug?.updatedAt)
      : strings.NEWS_RECOMMENDED_DAYS + 1
    if (foundSlug && daysDifference <= strings.NEWS_RECOMMENDED_DAYS) {
      return {
        news: {
          title: foundSlug.title,
          publicationName: foundSlug.publisher,
          publicationLanguage: 'en',
          date: foundSlug.updatedAt,
        },
        ...originalPathParams,
      }
    }
    return originalPathParams
  } catch (error) {
    logCommonError(`Couldn't generate news paths ${locPath}`, error)
    return originalPathParams
  }
}

module.exports = { generateNewsAndBlogSitemap }
