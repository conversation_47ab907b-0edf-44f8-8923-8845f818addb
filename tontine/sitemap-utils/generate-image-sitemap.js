const path = require('path')
const { logCommonError } = require('./SitemapUtilFunctions')

/**
 * This function generates an image sitemap based on the provided path parameters and location path.
 *
 * - `config` - The configuration object for the sitemap.
 * - `originalPathParams` - The original path parameters.
 * - `locPath` - The location path.
 *
 * Returns the original path parameters possibly augmented with an 'images' property.
 * The 'images' property is an array of image objects, each with a 'loc' property that specifies the image location.
 * The 'loc' property is constructed using the path property of the images in the found image object.
 * If no matching image object is found, or if the found image object has no images, the original path parameters are returned unchanged.
 */
function generateImageSitemap(config, originalPathParams, locPath, imagesData) {
  try {
    const foundImageObject = imagesData?.find((item) => locPath === item?.slug)
    if (foundImageObject && foundImageObject?.images?.length > 0) {
      const images = foundImageObject?.images?.map((image) => {
        return {
          loc: new URL(path.join(config.siteUrl, image)),
        }
      })
      return {
        ...originalPathParams,
        images,
      }
    }
    return originalPathParams
  } catch (error) {
    logCommonError(`Couldn't generate image paths ${locPath}`, error)
    return originalPathParams
  }
}
module.exports = { generateImageSitemap }
