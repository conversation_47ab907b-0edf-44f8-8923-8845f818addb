const fs = require('fs')
const path = require('path')
const strings = require('../data-resource/strings.json')

// Returns path object from given config
function returnOriginalPathParams(config, locPath) {
  return {
    loc: locPath,
    changefreq: config.changefreq,
    priority: config.priority,
    lastmod: config.lastmod ? new Date().toISOString() : config.lastmod,
    alternateRefs: config.alternateRefs ?? [],
  }
}

/**
 * Joins an array of segments into a single path string, with optional leading and ending trailing slash.
 *
 * - `segments` - An array of segments to join into a path.
 * - `leadingSlash` - Whether to include a leading slash in the path. Defaults to false.
 * - `endsWithSlash` - Whether to include a trailing slash at the end of the path. Defaults to false.
 */
function generatePathWithTrailingSlash({
  segments,
  leadingSlash = false,
  endsWithSlash = false,
}) {
  const segmentPath = segments?.filter(Boolean).join('/')

  return `${leadingSlash ? '/' : ''}${segmentPath}${endsWithSlash ? '/' : ''}`
}

// Determines if current path is a news path
function isPathFromArticle(string) {
  const newsPath = generatePathWithTrailingSlash({
    segments: [strings.NEWS],
    leadingSlash: true,
    endsWithSlash: true,
  })

  const blogPath = generatePathWithTrailingSlash({
    segments: [strings.BLOG],
    leadingSlash: true,
    endsWithSlash: true,
  })

  if (string.startsWith(newsPath)) {
    return { isNews: true, isBlog: false }
  }

  if (string.startsWith(blogPath)) {
    return { isNews: false, isBlog: true }
  }

  return { isNews: false, isBlog: false }
}

/**
 * This function checks the difference in days between a given date and the current date.
 * - `dateStr` - The date string to check.
 *
 * Returns the difference in days if the date is within the last day and onwards, otherwise returns 0.
 */
function getDaysDifference(dateStr) {
  // Convert dateStr to a Date object
  const date = new Date(dateStr).getTime()

  // Get the current date and time
  const now = new Date().getTime()

  // Calculate the difference in days
  const diffInDays = Math.ceil((now - date) / (1_000 * 60 * 60 * 24))

  // Return the difference if it's 1 or more, otherwise return 0
  return diffInDays >= 1 ? diffInDays : 0
}

/**
 * Generates the file path for a sitemap file based on the given file name and backup flag for the backup directory.
 */
function generateSitemapFilePath(fileName, isBackup = false) {
  const directoryPath = isBackup
    ? strings.TEMP_SITEMAP_DATA_DIRECTORY_PATH_BACKUP
    : strings.TEMP_SITEMAP_DATA_DIRECTORY_PATH

  return `${generatePathWithTrailingSlash({ segments: [directoryPath, fileName] })}`
}

/**
 * Logs the error with a custom log tag.
 */
function logCommonError(message, error) {
  const logMessage = `TONTINE_CM_ERROR: ${message}`
  // biome-ignore lint/suspicious/noConsole: <explanation>
  console.error(logMessage, error?.message, error?.stack ? error.stack : '')
}

/**
 * Reads sitemap data from a file and if it doesn't exist, reads it from a backup file.
 */
function readSitemapData(fileName, useBackup = false) {
  try {
    const filePath = generateSitemapFilePath(fileName, useBackup)
    const data = fs.readFileSync(path.resolve(process.cwd(), filePath), 'utf8')
    return JSON.parse(data)
  } catch (error) {
    logCommonError(
      `Couldn't read ${useBackup ? 'backup' : 'primary'} data file.`,
      error
    )
    if (!useBackup) {
      // Try to read data with backup
      return readSitemapData(fileName, true)
    }
    return null
  }
}

module.exports = {
  returnOriginalPathParams,
  isPathFromArticle,
  getDaysDifference,
  generateSitemapFilePath,
  readSitemapData,
  logCommonError,
}
