const {
  returnOriginalPathParams,
  isPathFromArticle,
  logCommonError,
} = require('./SitemapUtilFunctions')

const { replaceLastModSitemap } = require('./replace-last-mod-sitemap')
const { generateVideoSitemap } = require('./generate-video-sitemap')
const { generateImageSitemap } = require('./generate-image-sitemap')
const {
  generateNewsAndBlogSitemap,
} = require('./generate-news-and-blog-sitemap')

/**
 * Transforms sitemap paths based on the provided configuration and location path.
 *
 * - `config` - The configuration object for the sitemap.
 * - `locPath` - The location path for the sitemap.
 *
 * This function first retrieves the original path parameters using the `returnOriginalPathParams` function.
 * It then checks if the path is from an article using the `isPathFromArticle` function.
 * Depending on the result, it modifies the path parameters using the `replaceLastModSitemap`, `generateVideoSitemap`, and `generateImageSitemap` functions.
 * If the path is from an article, it further modifies the path parameters using the `generateNewsSitemap` function.
 *
 * Returns an object containing the modified sitemap entry properties.
 * If an error occurs during the process, it logs the error message and returns the original path parameters.
 */
function transformSitemapPaths(config, locPath, sitemapData) {
  const originalPathParams = returnOriginalPathParams(config, locPath)

  try {
    const { isNews, isBlog } = isPathFromArticle(locPath)

    const modifiedLastModPathParams = replaceLastModSitemap(
      originalPathParams,
      locPath,
      sitemapData.lastModData
    )
    const modifiedVideoPathParams = generateVideoSitemap(
      config,
      modifiedLastModPathParams,
      locPath,
      sitemapData.videosData
    )
    const modifiedImagePathParams = generateImageSitemap(
      config,
      modifiedVideoPathParams,
      locPath,
      sitemapData.imagesData
    )

    if (isNews) {
      return generateNewsAndBlogSitemap(
        modifiedImagePathParams,
        locPath,
        sitemapData.newsData
      )
    }
    if (isBlog) {
      return generateNewsAndBlogSitemap(
        modifiedImagePathParams,
        locPath,
        sitemapData.newsData
      )
    }

    return modifiedImagePathParams
  } catch (error) {
    logCommonError(`Failed to transform path ${locPath}`, error)
    return originalPathParams
  }
}

module.exports = { transformSitemapPaths }
