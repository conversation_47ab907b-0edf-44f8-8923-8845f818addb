const { logCommonError } = require('./SitemapUtilFunctions')

/**
 * Replaces the lastmod value in the sitemap with the updatedAt value from Sanity.
 */
function replaceLastModSitemap(originalPathParams, locPath, lastModData) {
  try {
    const foundSlug = lastModData?.find((item) => locPath === item?.slug)

    if (foundSlug) {
      return {
        ...originalPathParams,
        lastmod: foundSlug.updatedAt,
      }
    }
    return originalPathParams
  } catch (error) {
    logCommonError(`Couldn't configure the lastmod property ${locPath}`, error)
    return originalPathParams
  }
}

module.exports = { replaceLastModSitemap }
