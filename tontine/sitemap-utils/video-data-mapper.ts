import { promises as fsPromises } from 'fs'
import path from 'path'

import strings from '../data-resource/strings.json'
import {
  convertSanityURLToNext,
  convertToPlainText,
  generatePathWithTrailingSlash,
} from '../helper-functions/UtilFunctions'
import { logServerless } from '../serverless/ApiUtilFunctions'
import type { ContentPost } from '../types/sections/content-section.types'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
} from '../types/sections/section.types'

/**
 * Asynchronously transforms the input array of ArticleCardItem objects and writes the transformed data to a JSON file.
 *
 * - `inputArray` - An array of ArticleCardItem objects to be transformed.
 *
 * The function maps over the input array and transforms each item into an object with properties relevant for video sitemap generation. It then writes this transformed array to a JSON file in the specified directory. If the directory does not exist, it creates it.
 *
 * If an error occurs during this process, it logs the error message using the `logServerless` function.
 */
export async function transformAndWriteVideos(
  inputArray: Array<ContentPost>
): Promise<void> {
  try {
    const filteredVideoData = inputArray?.filter(
      (e) => e?.videoThumbnail
    ) as unknown as Array<{
      slug: { current: string }
      title: LocalizedStringContentType
      subtitle: LocalizedContentType
      videoThumbnail: string
      playbackId: string
      duration: number
    }>
    const outputArray = filteredVideoData?.map((item) => ({
      videoThumbnail: convertSanityURLToNext(item?.videoThumbnail),
      videoWatchTime: Math.trunc(item?.duration ?? 1),
      videoSummary: convertToPlainText({ value: item?.subtitle }),
      videoTitle: convertToPlainText({ value: item?.title }),
      videoSectionSlug: item?.slug.current,
      playbackId: item?.playbackId,
    }))

    const dir = path.join(
      process.cwd(),
      generatePathWithTrailingSlash({
        segments: ['tontine', strings.TEMP_SITEMAP_DATA_DIRECTORY_PATH],
      })
    )

    await fsPromises.mkdir(dir, { recursive: true })

    await fsPromises.writeFile(
      path.join(dir, strings.SITEMAP_VIDEO_DATA_FILE_NAME),
      JSON.stringify(outputArray, null, 2)
    )
  } catch (error) {
    logServerless({
      message: 'Could not write videos data to JSON',
      error,
      logLevel: 'error',
    })
  }
}
