const fs = require('fs')
const path = require('path')
const strings = require('../data-resource/strings.json')
const { logCommonError } = require('./SitemapUtilFunctions')

const generatePdfSitemap = () => {
  try {
    const pdfBaseDirectory = path.join(process.cwd(), 'public', 'research')

    const researchDirectories = fs.readdirSync(pdfBaseDirectory, {
      withFileTypes: true,
    })

    const sitemaps = researchDirectories.flatMap((directory) => {
      if (directory.isDirectory()) {
        const articleSlug = directory.name
        const articleDirectory = path.join(pdfBaseDirectory, articleSlug)

        const pdfFiles = fs.readdirSync(articleDirectory)

        return pdfFiles
          .filter((pdfFile) => pdfFile.toLowerCase().endsWith('.pdf'))
          .map((pdfFile) => ({
            trailingSlash: false,
            loc: path.join(strings.RESEARCH, articleSlug, pdfFile),
            lastmod: new Date().toISOString(),
            changefreq: 'daily',
            priority: 0.7,
          }))
      }
      return []
    })

    // biome-ignore lint/suspicious/noConsole: <explanation>
    console.log(`TONTINE_SUCCESS: Successfully added PDF's files to sitemap.`)
    return sitemaps
  } catch (error) {
    logCommonError(`Couldn't generate PDF sitemap`, error)
    return []
  }
}

module.exports = { generatePdfSitemap }
