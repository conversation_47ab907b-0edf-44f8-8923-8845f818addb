const path = require('path')
const strings = require('../data-resource/strings.json')
const { logCommonError } = require('./SitemapUtilFunctions')

/**
 * Generates video properties based on the provided location path.
 *
 * - `config` - The configuration object for the sitemap.
 * - `originalPathParams` - The original path object in the sitemap.
 * - `locPath` - The location path for the video.
 *
 * Returns an object containing video details if a matching video object is found.
 * If no matching video object is found, returns the original sitemap entry properties leaving the current entry unchanged.
 */
function generateVideoSitemap(config, originalPathParams, locPath, videoData) {
  try {
    const foundVideoObject = videoData?.find(
      (item) => locPath === `/${strings.VIDEOS}/${item?.videoSectionSlug}`
    )

    if (foundVideoObject) {
      return {
        videos: [
          {
            contentLoc: new URL(
              `https://stream.mux.com/${foundVideoObject.playbackId}.m3u8`
            ),
            thumbnailLoc: new URL(
              path.join(config.siteUrl, foundVideoObject.videoThumbnail)
            ),
            description:
              foundVideoObject.videoSummary ??
              'Tontines explained in less than 2 minutes',
            duration: foundVideoObject.videoWatchTime,
            title: foundVideoObject.videoTitle,
          },
        ],
        ...originalPathParams,
      }
    }
    return originalPathParams
  } catch (error) {
    logCommonError(`Couldn't generate video paths ${locPath}`, error)
    return originalPathParams
  }
}

module.exports = { generateVideoSitemap }
