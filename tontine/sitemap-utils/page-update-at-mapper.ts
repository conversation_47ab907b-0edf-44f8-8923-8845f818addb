import { promises as fsPromises } from 'fs'
import path from 'path'

import strings from '../data-resource/strings.json'
import {
  convertToPlainText,
  generatePathWithTrailingSlash,
} from '../helper-functions/UtilFunctions'
import { logServerless } from '../serverless/ApiUtilFunctions'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
} from '../types/sections/section.types'

/**
 * Asynchronously transforms the input array of ArticleCardItem objects and writes the transformed data to a JSON file.
 *
 * - `inputArray` - An array of ArticleCardItem objects to be transformed.
 *
 * The function maps over the input array and transforms each item into an object with properties relevant for video sitemap generation. It then writes this transformed array to a JSON file in the specified directory. If the directory does not exist, it creates it.
 *
 * If an error occurs during this process, it logs the error message using the `logServerless` function.
 */
export async function transformAndWritePageData(
  inputArray: Array<{
    slug: string
    updatedAt?: string
    title?: LocalizedStringContentType | LocalizedContentType
  }>
) {
  try {
    const outputArray = inputArray.map((page) => {
      const { slug, title, updatedAt } = page
      const slugPath = generatePathWithTrailingSlash({
        segments: [slug],
        leadingSlash: true,
        endsWithSlash: false,
      })
      if (
        slug?.includes(
          generatePathWithTrailingSlash({
            segments: [strings.NEWS],
            endsWithSlash: false,
          })
        ) ||
        slug?.includes(
          generatePathWithTrailingSlash({
            segments: [strings.BLOG],
            endsWithSlash: false,
          })
        )
      ) {
        return {
          title: convertToPlainText({ value: title }),
          publisher: strings.POST_PUBLISHER_PLACEHOLDER,
          slug: `${slugPath}`,
          updatedAt,
        }
      }

      return {
        slug: slug ? `${slugPath}` : '/',
        updatedAt,
      }
    })
    const dir = path.join(
      process.cwd(),
      generatePathWithTrailingSlash({
        segments: ['tontine', strings.TEMP_SITEMAP_DATA_DIRECTORY_PATH],
      })
    )

    await fsPromises.mkdir(dir, { recursive: true })

    await fsPromises.writeFile(
      path.join(dir, strings.SITEMAP_LAST_MOD_DATA_FILE_NAME),
      JSON.stringify(outputArray, null, 2)
    )
  } catch (error) {
    logServerless({
      message: 'Could not write page data to JSON',
      error,
      logLevel: 'error',
    })
  }
}
