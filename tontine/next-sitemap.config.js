const { generatePdfSitemap } = require('./sitemap-utils/generate-pdf-sitemap')
const strings = require('./data-resource/strings.json')
const {
  transformSitemapPaths,
} = require('./sitemap-utils/transform-sitemap-paths')
const { readSitemapData } = require('./sitemap-utils/SitemapUtilFunctions')

// Read additional data needed for sitemaps
const videoData = readSitemapData(strings.SITEMAP_VIDEO_DATA_FILE_NAME)
const imagesData = readSitemapData(strings.SITEMAP_IMAGE_DATA_FILE_NAME)
const lastModData = readSitemapData(strings.SITEMAP_LAST_MOD_DATA_FILE_NAME)

/** @type {import('next-sitemap').IConfig} */
module.exports = {
  // Base URL for the sitemap, depending on the USA_SITEMAP environment variable
  siteUrl: process.env.NETLIFY_WEBSITE_DOMAIN ?? strings.PLACEHOLDER_DOMAIN,
  exclude: process.env.EXCLUDE_PAGES,
  sitemapSize: 100,
  generateIndexSitemap: true,
  generateRobotsTxt: true,

  transform: async (config, path) => {
    return transformSitemapPaths(config, path, {
      videoData,
      newsData: lastModData,
      imagesData,
      lastModData,
    })
  },

  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
      },
      {
        userAgent: '*',
        disallow: process.env.EXCLUDE_PAGES
          ? JSON.parse(process.env.EXCLUDE_PAGES)
          : '',
      },
    ],
  },

  additionalPaths: async () => {
    const pdfSitemap = generatePdfSitemap()
    return pdfSitemap
  },
}
