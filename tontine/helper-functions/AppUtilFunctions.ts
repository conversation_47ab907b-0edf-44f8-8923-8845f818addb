import path from 'path'

import strings from '../data-resource/strings.json'
import { contentPostWriteDataQuery } from '../sanity-queries/content-post-queries/content-post-queries'
import { allImagesQuery } from '../sanity-queries/miscellaneous-queries/miscellaneous-queries'
import { fetchSanityData } from '../sanity-queries/query-fetch-function'
import { transformAndWritePageData } from '../sitemap-utils/page-update-at-mapper'
import { transformAndWriteVideos } from '../sitemap-utils/video-data-mapper'
import type {
  DynamicPageData,
  PageWithImages,
  PagesOnWebsite,
  StaticPageParamData,
} from '../types/pages.types'
import type { ContentPostFeedData } from '../types/sections/content-section.types'
import type { SectionType } from '../types/sections/section.types'
import { clearDirectory, fetchAndWriteFiles } from './FileDownloader'
import {
  mapResearchPDFs,
  processAndWriteImageJSON,
} from './MiscOpsUtilFunctions'
import { generatePathWithTrailingSlash } from './UtilFunctions'

/**
 * Processes the given pages and returns an array of objects.
 * Each object contains the slug of the page and, if available and additional object for each of the nested slugs.
 */
export function processPages(pages: StaticPageParamData) {
  return pages?.pagesOnWebsite
    .filter((page) => page?.pageSlug)
    .flatMap((page) => {
      const slugArray = page?.slugArray ?? []
      return [
        { slug: [page.pageSlug] },
        ...(Array.isArray(slugArray) && slugArray.length > 0
          ? slugArray.map((item) => ({
              slug: [page.pageSlug, item?.slug],
            }))
          : []),
      ]
    })
}

/** The function filters sections with posts and flattens their `postsArray` into a single array.
 * It also ensures each post has a `slug`.
 */
function flattenSubPages(data: Array<SectionType>) {
  // Filter out items with empty subPages array
  const filteredData = data?.filter((item) => item?.postsArray?.length > 0)
  // Flatten the subPages into one big array
  const flattenedSubPages = filteredData
    ?.flatMap((e) => e?.postsArray)
    ?.filter((e) => e?.slug)

  return flattenedSubPages
}

/**
 * Processes the given pages and returns an array of objects.
 * Each object contains the slug of the page and, if available and additional object for each of the nested slugs.
 */
function processLastModPages(pages: PagesOnWebsite) {
  return pages?.flatMap((page) => {
    const slugArray = page?.slugArray ?? []
    return [
      { slug: page?.pageSlug, updatedAt: page?.updatedAt },
      ...(Array.isArray(slugArray) && slugArray?.length > 0
        ? slugArray.map((item) => ({
            slug: generatePathWithTrailingSlash({
              segments: [page.pageSlug, item.slug],
            }),
            updatedAt: item.updatedAt,
            title: item.title,
          }))
        : []),
    ]
  })
}

/**
 * Processes content posts to generate a list of formatted post data.
 *
 * This function takes an array of pages and processes each page to generate a
 * list of content post objects. Each content post object contains the slug, title,
 * description, and date of the post.
 */
export function processContentPosts(
  pages: ContentPostFeedData['pagesOnWebsite']
) {
  return pages.flatMap((page) => [
    ...(Array.isArray(page.subPages) && page.subPages.length > 0
      ? page.subPages.map((item) => ({
          slug: generatePathWithTrailingSlash({
            segments: [page.pageSlug, item.slug],
          }),
          title: item.title,
          description: item.subtitle,
          date: item.updatedAt,
          image: item.postImageUrl,
          videoFile: {
            playbackId: item.videoFile?.playbackId,
            duration: item.videoFile?.duration,
            width: item.videoFile?.width,
            height: item.videoFile?.height,
          },
        }))
      : []),
  ])
}

/** The function performs several operations if the `BUILD_ENV` environment variable is set:
 * 1. Fetches and transforms page data.
 * 2. Clears the directory and writes page slugs.
 * 3. Fetches all images.
 * 4. Fetches, filters, and maps research and video data.
 * 5. Clears the directory and writes files for mapped research items.
 * 6. Transforms and writes videos.
 *
 * - `pages` - An array of `flattenSubPages()`.
 */
export async function writePagesData({
  pagesData,
}: {
  pagesData: StaticPageParamData
}) {
  await transformAndWritePageData(
    processLastModPages(pagesData?.pagesOnWebsite)
  )

  // PDF and Video data fetching and transformation
  const researchAndVideoData = await fetchSanityData<DynamicPageData>({
    query: contentPostWriteDataQuery,
  })

  const flattenedResearchAndVideoData = flattenSubPages(
    researchAndVideoData?.pagesOnWebsite as Array<SectionType>
  )

  // PDF files generation
  const mappedResearchItems = mapResearchPDFs(flattenedResearchAndVideoData)
  const pdfDir = path.join('tontine/public', strings.RESEARCH_PDF_PATH)
  // Delete PDF files so there isn't any leftover from previous build
  await clearDirectory(pdfDir)
  await fetchAndWriteFiles({
    items: mappedResearchItems,
    directory: pdfDir,
  })

  // Video JSON generation
  await transformAndWriteVideos(flattenedResearchAndVideoData)

  // Image JSON generation
  const pagesWithImages = await fetchSanityData<PageWithImages>({
    query: allImagesQuery,
  })

  const imageJSONdir = path.join(
    process.cwd(),
    'tontine',
    strings.TEMP_SITEMAP_DATA_DIRECTORY_PATH,
    strings.SITEMAP_IMAGE_DATA_FILE_NAME
  )

  await processAndWriteImageJSON({
    pagesArray: pagesWithImages.pagesOnWebsite,
    directory: imageJSONdir,
  })
}
