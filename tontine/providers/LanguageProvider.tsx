'use client'

import type { ReactNode } from 'react'
import { useEffect, useState } from 'react'

import { CONSTANTS } from '../data-resource/constants'
import { isLoadedWindow } from '../helper-functions/UtilFunctions'
import { defaultLanguage } from '../serverless/keys'
import type { LanguagesType } from '../types/common.types'
import { LanguageContext } from './LanguageContext'

/** The languageProvider component is responsible for managing the language.
 * It sets the language by default to the defaultLanguage and updates localStorage
 * if the language changes.
 *
 * The provider also provides a setLanguage function that can be used to update
 * the language and update localStorage.
 *
 * Additionally, the provider sets the language to the value stored in localStorage
 * if a value exists.
 *
 * The provider only updates localStorage if the language changes.
 */
export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguageState] = useState<LanguagesType>(defaultLanguage)
  const [isLoaded, setIsLoaded] = useState(false)

  /** Sets the language and updates localStorage if necessary. */
  const setLanguage = (newLanguage: LanguagesType) => {
    if (language !== newLanguage) {
      setLanguageState(newLanguage)
      // Update localStorage with the new language
      if (isLoadedWindow()) {
        localStorage.setItem('language', newLanguage)
      }
    }
    setIsLoaded(true)
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: Will cause unnecessary re-renders
  useEffect(() => {
    const initializeLanguage = () => {
      if (isLoadedWindow()) {
        // Get language from localStorage
        const storedLanguage = localStorage.getItem('language') as LanguagesType
        setLanguage(storedLanguage ?? CONSTANTS.LANGUAGES[0])
      }
    }

    initializeLanguage()
  }, [])

  return (
    <LanguageContext.Provider value={{ language, isLoaded, setLanguage }}>
      {children}
    </LanguageContext.Provider>
  )
}
