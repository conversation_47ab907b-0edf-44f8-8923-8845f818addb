import { CONSTANTS } from '../data-resource/constants'
import { buildContext, isProd } from '../serverless/keys'

const getMTLOrigin = () => {
  if (isProd) return CONSTANTS.MY_TONTINE_LITE_PROD
  if (buildContext === 'staging') return CONSTANTS.MY_TONTINE_LITE_STAGING
  return CONSTANTS.MY_TONTINE_LITE_DEV
}

/**
 * Changes the MyTontine lite origin depending on the environment
 */
const myTontineLiteOrigin = getMTLOrigin()

export { myTontineLiteOrigin }
