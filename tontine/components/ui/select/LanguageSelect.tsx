import { CONSTANTS } from '../../../data-resource/constants'
import { cn } from '../../../helper-functions/UtilFunctions'
import type { LanguagesType } from '../../../types/common.types'
import { Select } from './Select'

/** LanguageSelect component which renders a language selector. */
export const LanguageSelect = ({
  language,
  setLanguage,
}: {
  language: LanguagesType
  setLanguage: (lang: LanguagesType) => void
}) => {
  const handleLanguageSelect = (lang: LanguagesType) => setLanguage(lang)

  return (
    <Select
      clearable={false}
      className={cn(
        'mr-3 ml-auto w-fit bg-grey-200 text-grey-650 opacity-90 transition-all hover:opacity-100'
      )}
      triggerProps={{
        className: 'text-sm lg:text-base p-2',
      }}
      selectListProps={{
        className: 'flex flex-col',
      }}
      buttonProps={{
        className: 'justify-center p-0 py-2',
      }}
      placeholder='lang'
      defaultOption={{
        value: language,
        label: language?.toUpperCase() ?? CONSTANTS.LANGUAGES[0].toUpperCase(),
      }}
      setSelectedValue={handleLanguageSelect}
      options={CONSTANTS.LANGUAGES?.map((lang) => {
        return {
          value: lang,
          label: lang.toUpperCase(),
        }
      })}
    />
  )
}
