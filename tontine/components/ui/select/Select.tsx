'use client'

import { ChevronDownIcon } from '@sanity/icons'
import { useState } from 'react'

import { cn } from '../../../helper-functions/UtilFunctions'
import type {
  SelectOptionType,
  SelectProps,
} from '../../../types/components/Select.types'
import { BoxContainer } from '../../common/BoxContainer'
import { GenericButton } from '../../common/GenericButton'
import { CloseButton } from '../CloseButton'
import { SelectOptionList } from './SelectOptionList'

/** A component that renders a select dropdown with a button as the trigger.
 * It receives a list of options and displays the selected option's label in the button.
 * It also receives a callback that is triggered when the user selects an option.
 */
export const Select = <T = string>({
  options,
  placeholder = 'Select...',
  clearable = false,
  hideChevron,
  selectListProps,
  defaultOption,
  buttonProps,
  triggerProps,
  setSelectedValue,
  ...rest
}: SelectProps<T>) => {
  const [selectedOption, setSelectedOption] =
    useState<SelectOptionType<T> | null>(defaultOption ?? null)
  const [listOpen, setListOpen] = useState(false)

  const handleSelect = (option: SelectOptionType<T>) => {
    setSelectedOption(option)
    setSelectedValue?.(option.value)
    setListOpen(!listOpen)
  }

  const handleClear = () => {
    setSelectedOption(null)
    setSelectedValue?.('' as T)
  }

  const handleToggle = () => {
    setListOpen(!listOpen)
  }

  return (
    <BoxContainer
      {...rest}
      className={cn(
        'group/select relative flex w-full cursor-pointer items-center rounded-md bg-background-100 text-gray-700',
        rest.className
      )}
      data-open={listOpen}
      data-valid={Boolean(selectedOption)}
    >
      <GenericButton
        type='button'
        onClick={handleToggle}
        aria-expanded={listOpen}
        {...triggerProps}
        className={cn(
          'group/trigger flex-grow justify-start rounded-sm p-2 text-left text-base leading-4 md:leading-[inherit]',
          triggerProps?.className
        )}
      >
        {selectedOption ? selectedOption.label : placeholder}
        {!hideChevron && (
          <ChevronDownIcon
            className={cn(
              'ml-auto h-6 w-6 transition duration-100 group-aria-expanded/trigger:rotate-180'
            )}
          />
        )}
      </GenericButton>
      {clearable && selectedOption && (
        <CloseButton
          type='button'
          onClick={handleClear}
          className='p-2 text-gray-500 hover:text-gray-700 focus:outline-none'
          iconProps={{
            className: 'h-6 w-6',
          }}
        />
      )}
      {listOpen && (
        <SelectOptionList<T>
          {...selectListProps}
          buttonProps={buttonProps}
          options={options}
          handleSelect={handleSelect}
        />
      )}
    </BoxContainer>
  )
}
