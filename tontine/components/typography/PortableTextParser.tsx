import type {
  PortableTextBlock,
  PortableTextComponentProps,
  PortableTextMarkComponentProps,
} from 'next-sanity'

import type { SanityImageType } from '../../types/common.types'
import type {
  ComponentParserType,
  TableComponentProps,
} from '../../types/components/PortableText.types'
import { NextLink } from '../common/NextLink'
import { SanityImage } from '../common/SanityImage'
import { DynamicMetric } from './DynamicMetric'
import { Table } from './Table'
import { TooltipWrapper, type TooltipWrapperProps } from './ToolTip'

const tooltipMark = ({
  children,
  value,
}: {
  children: React.ReactNode
  value?: TooltipWrapperProps
}) => <TooltipWrapper {...value}>{children}</TooltipWrapper>

/**
 * A function that returns a configuration object for rendering Portable Text components.
 * It includes custom rendering for specific types like images and dynamic metrics,
 * as well as support for custom marks (e.g., color picker and links) and block-level overrides.
 */
export const portableTextParser = ({
  renderDefaultBlock = true,
  overrideElements,
  parentSectionId,
}: ComponentParserType) => {
  return {
    types: {
      image: ({
        value,
      }: PortableTextComponentProps<{
        asset: SanityImageType & { aspectRatio: number }
      }>) => {
        return (
          <SanityImage
            src={value?.asset?.url}
            alt={value?.asset?.altText ?? ''}
            fillProp
            skeletonProps={{
              style: { aspectRatio: value?.asset?.aspectRatio },
            }}
          />
        )
      },
      table: (props: TableComponentProps) => <Table {...props} />,
      georgeYield: ({ value }: { value: { george?: string } }) => (
        <DynamicMetric metric={value?.george} />
      ),
      returnRate: ({ value }: { value: { rate?: string } }) => (
        <DynamicMetric metric={value?.rate} />
      ),
      year: ({ value }: { value: { year?: string } }) => <>{value?.year}</>,
    },
    marks: {
      link: ({ children, value }: PortableTextMarkComponentProps) => {
        return (
          <NextLink
            externalIconProps={{ className: 'w-5 h-5' }}
            className='gap-0'
            objectId={parentSectionId}
            href={value?.url}
          >
            {children}
          </NextLink>
        )
      },
      colorPicker: ({ children, value }: PortableTextMarkComponentProps) => (
        <span style={{ color: value?.color || 'inherit' }}>{children}</span>
      ),
      tooltipWrapper: tooltipMark,
      tooltip: tooltipMark,
      glossary: tooltipMark,
    },

    block: {
      normal: ({
        children,
        value,
      }: PortableTextComponentProps<PortableTextBlock>) => {
        if (
          value.children?.some(
            (child) =>
              child._type === 'image' ||
              child._type === 'table' ||
              !renderDefaultBlock
          )
        ) {
          return <>{children}</>
        }

        const defaultElement = 'p'

        const Tag = overrideElements?.p?.tag ?? defaultElement

        return <Tag className={overrideElements?.p?.className}>{children}</Tag>
      },
    },
  }
}
