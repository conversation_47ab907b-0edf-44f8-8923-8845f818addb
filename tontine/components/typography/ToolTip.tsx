import type {
  LocalizedContentType,
  LocalizedStringContentType,
} from '../../types/sections/section.types'
import { BoxContainer } from '../common/BoxContainer'
import { LocalizedContentParser } from './LocalizedContentParser'
import { WrappedText } from './WrappedText'

export type TooltipWrapperProps = {
  type?: string
  children?: React.ReactNode
  textTooltip?: LocalizedContentType
  glossaryTooltip?: {
    title?: LocalizedStringContentType
    subtitle?: LocalizedContentType
  }
}

/**
 * A wrapper component that renders a tooltip on hover
 * Accepts either a custom text tooltip or a glossary tooltip content.
 */
export const TooltipWrapper = ({
  children,
  type,
  textTooltip,
  glossaryTooltip,
}: TooltipWrapperProps) => {
  const tooltipContent =
    type === 'text' ? textTooltip : glossaryTooltip?.subtitle

  if (!tooltipContent) return <>{children}</>

  return (
    <BoxContainer as='span' className='group relative inline-block'>
      <WrappedText as='span' className='cursor-help text-brand'>
        {children}
      </WrappedText>

      <BoxContainer
        as='span'
        className='invisible absolute bottom-9 left-0 flex h-max w-max max-w-lg flex-col rounded-sm border border-gray-400 bg-background p-2 text-base opacity-0 shadow-md transition-opacity group-hover:visible group-hover:opacity-100'
      >
        <BoxContainer
          as='span'
          className='-bottom-[0.42rem] absolute h-3 w-3 rotate-225 border-gray-400 border-t border-l bg-background'
        />
        <LocalizedContentParser
          overrideElements={{
            p: {
              tag: 'span',
              className: 'leading-6',
            },
          }}
        >
          {tooltipContent}
        </LocalizedContentParser>
      </BoxContainer>
    </BoxContainer>
  )
}
