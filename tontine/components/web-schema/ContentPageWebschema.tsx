import path from 'path'
import type { NewsArticle, VideoObject, WithContext } from 'schema-dts'

import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import strings from '../../data-resource/strings.json'
import {
  convertSanityURLToNext,
  convertToISO,
  convertToPlainText,
  formatTimeHoursMinutesSecondsForWebschema,
  getAttribute,
  getMuxContentUrl,
} from '../../helper-functions/UtilFunctions'
import type { PageDomainType } from '../../types/common.types'
import type { ContentPost } from '../../types/sections/content-section.types'

type ContentPageWebschemaProps = {
  webSchemaData?: ContentPost
} & PageDomainType
/**
 * ContentPageWebschema is a component that renders the JSON-LD schema for the a single content post.
 * It is used to provide information about the website to search engines.
 */
export function ContentPageWebschema({
  webSchemaData,
  pageDomain = '',
}: ContentPageWebschemaProps) {
  const isVideo = Boolean(webSchemaData?.videoFile)

  const videoProps: WithContext<VideoObject> = {
    '@context': 'https://schema.org',
    '@type': 'VideoObject',
    name: convertToPlainText({ value: webSchemaData?.title }),
    description: convertToPlainText({ value: webSchemaData?.subtitle }),
    duration: formatTimeHoursMinutesSecondsForWebschema(
      Number(webSchemaData?.videoFile?.duration.toFixed(0))
    ),
    videoQuality: webSchemaData?.videoFile?.quality,
    width: `${webSchemaData?.videoFile?.width}`,
    height: `${webSchemaData?.videoFile?.height}`,
    contentUrl: getMuxContentUrl(webSchemaData?.videoFile?.playbackId ?? ''),
    thumbnailUrl: path.join(
      pageDomain,
      convertSanityURLToNext(
        webSchemaData?.postImage?.url ??
          webSchemaData?.videoThumbnail?.url ??
          ''
      )
    ),
    uploadDate: webSchemaData?.videoFile?.createdAt,
    dateModified: webSchemaData?.videoFile?.updatedAt,
    dateCreated: webSchemaData?.videoFile?.createdAt,
  }

  const newsArticleProps: WithContext<NewsArticle> = {
    '@context': 'https://schema.org',
    '@type': 'NewsArticle',
    author: webSchemaData?.authors?.map((author) => {
      return {
        '@type': 'Person',
        name: author?.personName ?? strings.POST_PUBLISHER_PLACEHOLDER,
        url:
          author?.personSocialLink?.url ?? strings.POST_AUTHOR_URL_PLACEHOLDER,
      }
    }) ?? {
      '@type': 'Person',
      name: strings.POST_PUBLISHER_PLACEHOLDER,
      url: strings.POST_AUTHOR_URL_PLACEHOLDER,
    },
    headline: convertToPlainText({ value: webSchemaData?.title }),
    datePublished: convertToISO(webSchemaData?.publishDate),
    image: path.join(
      pageDomain,
      convertSanityURLToNext(webSchemaData?.postImage?.url ?? '')
    ),
  }
  // converting the JSON-LD schema to a string
  const jsonLdString: string = JSON.stringify(
    isVideo ? videoProps : newsArticleProps
  )
  const scriptId = isVideo
    ? webSchemaData?.videoFile?.playbackId
    : `${webSchemaData?.slug.current}-webschema`

  return (
    <script
      id={scriptId}
      type='application/ld+json'
      // biome-ignore lint/security/noDangerouslySetInnerHtml: <Need to do this>
      dangerouslySetInnerHTML={{ __html: jsonLdString }}
      {...getAttribute(
        Boolean(UI_TEST_ID?.webschemaScript),
        'data-cy',
        UI_TEST_ID?.webschemaScript
      )}
    />
  )
}
