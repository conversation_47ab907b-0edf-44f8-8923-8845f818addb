import path from 'path'
import type { ItemList, WithContext } from 'schema-dts'

import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import strings from '../../data-resource/strings.json'
import {
  convertSanityURLToNext,
  convertToISO,
  convertToPlainText,
  getAttribute,
} from '../../helper-functions/UtilFunctions'
import type { PageDomainType } from '../../types/common.types'
import type { ContentPost } from '../../types/sections/content-section.types'

type ContentPageGalleryWebschemaProps = {
  webSchemaData?: Array<ContentPost>
  pageSlug: string
} & PageDomainType

/**
 * ContentPageGalleryWebschema is a component that renders the JSON-LD schema for the websites content pages gallery.
 * It is used to provide information about the website to search engines.
 */
export function ContentPageGalleryWebschema({
  webSchemaData,
  pageSlug,
  pageDomain = '',
}: ContentPageGalleryWebschemaProps) {
  // setting up the JSON-LD schema
  const jsonLd: WithContext<ItemList> = {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    name: 'Post Gallery',
    itemListElement: webSchemaData?.map((post, index: number) => {
      return {
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'NewsArticle',
          headline: convertToPlainText({ value: post?.title }),
          author: post?.authors?.map((author) => {
            return {
              '@type': 'Person',
              name: author?.personName ?? strings.POST_PUBLISHER_PLACEHOLDER,
              url:
                author?.personSocialLink?.url ??
                strings.POST_AUTHOR_URL_PLACEHOLDER,
            }
          }) ?? {
            '@type': 'Person',
            name: strings.POST_PUBLISHER_PLACEHOLDER,
            url: strings.POST_AUTHOR_URL_PLACEHOLDER,
          },
          description: convertToPlainText({ value: post?.body }),
          image: path.join(
            pageDomain,
            convertSanityURLToNext(post?.postImage?.url)
          ),
          url: path.join(pageDomain, pageSlug, post?.slug?.current, '/'),
          datePublished: convertToISO(post?.publishDate),
          dateModified: post?._updatedAt,
        },
      }
    }),
  }

  // converting the JSON-LD schema to a string
  const jsonLdString: string = JSON.stringify(jsonLd)
  return (
    <script
      id={`${pageSlug}-gallery-webschema`}
      type='application/ld+json'
      // biome-ignore lint/security/noDangerouslySetInnerHtml: <Need to do this>
      dangerouslySetInnerHTML={{ __html: jsonLdString }}
      {...getAttribute(
        Boolean(UI_TEST_ID?.webschemaScript),
        'data-cy',
        UI_TEST_ID?.webschemaScript
      )}
    />
  )
}
