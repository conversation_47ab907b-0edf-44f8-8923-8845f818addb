import { UI_TEST_ID } from '../../data-resource/data-test-ids'
import {
  convertDateToClientLocale,
  getReadWatchTimeString,
} from '../../helper-functions/UtilFunctions'
import { MoreLikeThis } from '../../sections/ContentPost/MoreLikeThis'
import { ShareSection } from '../../sections/Share/ShareSection'
import { ContentPostEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { AnalyticsObjectIds } from '../../types/Analytics/AnalyticsObjectIds.types'
import type { PageDomainType } from '../../types/common.types'
import type { ContentPost } from '../../types/sections/content-section.types'
import type { SharedSectionDataType } from '../../types/sections/section.types'
import { BoxContainer } from '../common/BoxContainer'
import { ContentPostLayout } from '../layouts/ContentPostLayout'
import { ContentPageWebschema } from '../web-schema/ContentPageWebschema'

type DynamicPageContentProps = {
  singleItemData?: ContentPost
  moreLikeThis: Array<ContentPost>
  shareSectionData: SharedSectionDataType
  parentSlug: string
  isFeatured: boolean
} & PageDomainType

/** The `DynamicPageContent` is rendering navigation menu, singleItemDataCollection and
 * footer. This component is called when we want to render page that contains
 * all data on research/article/video
 * - `homePageData` is data provided, mostly used for navigation menu,
 * footer, webSchema and favicon.
 * - `singleItemData` is data provided for single element it has to be from
 * type ArticleCardItem which means can be article/research or video
 */
export function DynamicPageContent({
  singleItemData,
  moreLikeThis,
  shareSectionData,
  parentSlug,
  isFeatured,
  pageDomain,
}: DynamicPageContentProps) {
  const objectId: AnalyticsObjectIds = `${singleItemData?.slug?.current}_content_post`

  return (
    <BoxContainer
      className='section sectionWrapper'
      dataTestId={UI_TEST_ID?.sectionWrapper}
      as={'main'}
    >
      <BoxContainer
        dataTestId={UI_TEST_ID?.postContent}
        as={'section'}
        className='w-full text-center'
      >
        {singleItemData && (
          <ContentPostLayout
            postTitle={singleItemData?.title}
            postSummary={singleItemData?.subtitle}
            authors={singleItemData?.authors}
            postImage={singleItemData?.postImage?.url}
            postBodyMarkdown={singleItemData?.body}
            postDate={convertDateToClientLocale(
              singleItemData?.videoFile?.createdAt ??
                singleItemData?.publishDate ??
                ''
            )}
            postReadTime={getReadWatchTimeString(singleItemData)}
            featuredPost={isFeatured}
            researchPdfFile={
              singleItemData?.manuscript?.asset?.originalFilename
            }
            videoThumbnail={singleItemData?.videoThumbnail?.url}
            videoId={singleItemData?.videoFile?.playbackId}
            videoDuration={singleItemData?.videoFile?.duration}
            parentSlug={parentSlug}
            slug={singleItemData.slug.current}
            parentSectionId={objectId}
          >
            <ContentPageWebschema
              webSchemaData={singleItemData}
              pageDomain={pageDomain}
            />
          </ContentPostLayout>
        )}
      </BoxContainer>
      <ShareSection
        title={shareSectionData?.title}
        subtitle={shareSectionData?.subtitle}
        slug={shareSectionData?.slug}
        pageDomain={pageDomain}
        shareEvent={ContentPostEvent.shared}
      />
      {singleItemData && (
        <MoreLikeThis
          parentSlug={parentSlug}
          postSlug={singleItemData.slug.current}
          block={singleItemData}
          moreLikeThis={moreLikeThis}
        />
      )}
    </BoxContainer>
  )
}
