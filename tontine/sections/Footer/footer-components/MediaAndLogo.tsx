import { BoxContainer } from '../../../components/common/BoxContainer'
import { SocialLink } from '../../../components/common/SocialLink'
import { FooterEvent } from '../../../types/Analytics/AnalyticsEvents.types'
import type {
  SanityImageType,
  SocialMediaIcon,
} from '../../../types/common.types'
import { FooterLogo } from './FooterLogo'

type MediaAndLogoProps = {
  img?: SanityImageType
  socialLinks?: Array<SocialMediaIcon>
  goHome: string
}

/**
 * MediaAndLogo is used to display the footer logo and social media icons
 */
export function MediaAndLogo({ img, socialLinks, goHome }: MediaAndLogoProps) {
  return (
    <BoxContainer className='flex flex-col items-center'>
      {img && (
        <FooterLogo
          alt={img?.altText}
          src={img?.url}
          href={goHome}
          parentSectionId='footer_main_link'
        />
      )}
      <BoxContainer
        as={'ul'}
        className='flex items-center justify-center gap-0.5'
      >
        {socialLinks?.length &&
          socialLinks?.map((item: SocialMediaIcon) => {
            return (
              <SocialLink
                key={`socialLink-${item?.url}`}
                url={item?.url}
                alt={item?.icon?.altText}
                title={item?.title}
                imgUrl={item?.icon?.url}
                parentSectionId='footer_social_link'
                customEvent={FooterEvent.social_click}
              />
            )
          })}
      </BoxContainer>
    </BoxContainer>
  )
}
