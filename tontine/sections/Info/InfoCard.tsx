import {
  BoxContainer,
  type BoxContainerProps,
} from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import { Card } from '../../components/ui/Card'
import { cn, generateSlug } from '../../helper-functions/UtilFunctions'
import type {
  InfoBlock,
  InfoVariants,
} from '../../types/sections/info-section.types'
import type { ParentSectionID } from '../../types/sections/section.types'

type InfoCardProps = {
  infoBlock?: InfoBlock
  variant?: InfoVariants
} & ParentSectionID &
  BoxContainerProps

/** `InfoBlockContainer` serve as a container for information's on info block */
export function InfoCard({
  infoBlock,
  parentSectionId,
  variant,
  ...rest
}: InfoCardProps) {
  const isHorizontal = variant === 'horizontal'
  const isVertical = variant === 'vertical'

  return (
    <Card
      {...rest}
      className={cn(
        'rounded-md bg-background-100 p-4 shadow-md transition-all hover:shadow-brand-200',
        isHorizontal && 'flex flex-col items-center gap-2 bg-unset shadow-none',
        isVertical &&
          'relative flex flex-col rounded-xl p-4 text-center lg:items-start lg:text-left xl:min-h-70 xl:p-6',
        !isHorizontal && !isVertical && 'min-h-55'
      )}
    >
      <Card.Header
        className={cn(
          'flex items-center justify-between gap-3',
          isHorizontal && 'relative flex-col-reverse gap-3',
          isVertical && 'flex flex-col'
        )}
      >
        <Title
          className={cn(
            'w-fit font-semibold text-brand text-xl',
            isHorizontal && 'text-center font-bold text-3xl text-fg',
            isVertical && 'order-1 font-bold text-2xl text-fg xl:text-4xl'
          )}
        >
          {infoBlock?.title}
        </Title>

        {infoBlock?.icon && (
          <SanityImage
            skeletonProps={{
              className: cn(
                'z-10 h-9 w-9',
                isHorizontal && 'h-30 w-35 p-4',
                isVertical &&
                  'top-0 right-0 bottom-0 order-0 my-auto mr-4 xxl:mr-10 h-50 w-50 lg:absolute'
              ),
            }}
            fillProp
            src={infoBlock?.icon?.url}
          />
        )}
      </Card.Header>
      <Card.Body
        className={cn(
          'flex flex-col',
          isHorizontal &&
            'w-auto xxl:w-[50%] max-w-[90%] gap-4 lg:max-w-[100%]',
          isVertical && 'h-full xxl:w-[50%] justify-between lg:w-[60%]'
        )}
      >
        <LocalizedContentParser
          renderWrapper
          className={cn(
            'py-4 text-grey-650 [&>ul>li]:pl-6 [&>ul>li]:text-left sm:[&>ul>li]:pl-6 sm:[&>ul>li]:text-center lg:[&>ul>li]:pl-8 lg:[&>ul>li]:text-left ',
            isHorizontal && 'py-0 text-center text-xl leading-6',
            isVertical && 'py-0 sm:px-10 md:px-30 md:text-lg lg:px-0 xl:text-xl'
          )}
          overrideElements={{
            li: { className: 'text-left' },
          }}
          parentSectionId={parentSectionId}
        >
          {infoBlock?.subtitle}
        </LocalizedContentParser>
        <BoxContainer className='lg:flex lg:gap-2'>
          {infoBlock?.infoBlockButtons?.map((button, index) => {
            const buttonSlug = generateSlug(button)
            return (
              <NextLink
                key={`info-block-button-${index}-${buttonSlug}`}
                href={buttonSlug}
                className={cn(
                  'button solid mx-auto w-full px-4 py-2 font-light text-base transition-all duration-200 md:w-fit md:min-w-1/4 lg:mx-0',
                  !isHorizontal && !isVertical && 'solid',
                  isHorizontal && 'lg:mx-auto',
                  isVertical && 'mt-2'
                )}
              >
                <LocalizedContentParser>
                  {button.buttonLabel}
                </LocalizedContentParser>
              </NextLink>
            )
          })}
        </BoxContainer>
      </Card.Body>
    </Card>
  )
}
