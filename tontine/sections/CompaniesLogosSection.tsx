import { BoxContainer } from '../components/common/BoxContainer'
import { SanityImage } from '../components/common/SanityImage'
import { LocalizedContentParser } from '../components/typography/LocalizedContentParser'
import { CONSTANTS } from '../data-resource/constants'
import type {
  CompaniesLogoType,
  SharedSectionDataType,
} from '../types/sections/section.types'

type CompaniesLogosSectionProps = {
  sectionData: {
    companyLogos?: Array<CompaniesLogoType>
  } & SharedSectionDataType
}

/**
 * `CompaniesLogosSection` which renders the companies logos
 */
export function CompaniesLogosSection({
  sectionData,
}: CompaniesLogosSectionProps) {
  return (
    <BoxContainer
      as={'section'}
      aria-label={sectionData?.slug?.current || 'In the media'}
      id={sectionData?.slug?.current}
      className='bg-background p-5 pb-10'
    >
      <LocalizedContentParser
        className='mb-2 text-center text-fg/80 sm:text-xl'
        renderWrapper
      >
        {sectionData?.title}
      </LocalizedContentParser>
      {sectionData?.companyLogos && sectionData?.companyLogos?.length > 0 && (
        <BoxContainer className='mx-auto flex w-full xxl:max-w-[50%] flex-wrap justify-center gap-2 lg:max-w-[80%] xl:max-w-[60%]'>
          {sectionData?.companyLogos?.map((company, index) => (
            <BoxContainer
              className='opacity-70 grayscale-0 filter transition-all hover:opacity-100 hover:filter'
              key={`company-image-main-page-${index}`}
            >
              <SanityImage
                src={company?.imageField?.url}
                alt={company?.imageField?.altText}
                skeletonProps={{
                  className: 'h-12 w-27 md:h-20 md:w-35 lg:w-40  lg:h-20 mx-2',
                }}
                fillProp
                sizes={CONSTANTS.NEXTJS_IMAGE_DEFAULT_SIZE_PROPERTY}
              />
            </BoxContainer>
          ))}
        </BoxContainer>
      )}
    </BoxContainer>
  )
}
