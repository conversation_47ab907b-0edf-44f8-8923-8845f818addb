import { LaunchIcon } from '@sanity/icons'

import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import { WrappedText } from '../../components/typography/WrappedText'
import { Badge } from '../../components/ui/Badge'
import { extractSiteName } from '../../helper-functions/UtilFunctions'
import type { SlugType } from '../../types/common.types'
import type { TestimonialCardType } from '../../types/sections/section.types'

type TestimonialCardProps = {
  profile: TestimonialCardType
  slug: SlugType
}

const headingTextStyling = 'text-base md:text-lg max-w-[60%]'

/** TestimonialCard component displays a testimonial with profile
 * image, name, profession, and optional link.
 * */
export function TestimonialCard({ profile, slug }: TestimonialCardProps) {
  return (
    <BoxContainer className='mb-4 max-w-[30rem] rounded-2xl bg-background-100 shadow-md'>
      <BoxContainer className='relative flex min-h-32 rounded-t-2xl bg-gradient-to-r from-brand-350 to-brand p-3.5 md:p-6'>
        <SanityImage
          alt={profile?.image?.altText}
          src={profile?.image?.url}
          fillProp
          skeletonProps={{
            className:
              'absolute h-25 w-25 md:h-30 md:w-30 object-cover -top-4 md:-top-6 rounded-full shadow-md',
          }}
          sizes='7.5rem'
          objectFitProp='cover'
        />

        <BoxContainer className='flex w-full flex-col items-end text-right text-background-100'>
          <Title
            className='font-bold text-lg md:text-xl'
            title={profile?.name}
          />
          {profile?.profession && (
            <WrappedText className={headingTextStyling}>
              {profile?.profession}
            </WrappedText>
          )}
          {profile?.workPlace && (
            <WrappedText className={headingTextStyling}>
              {profile?.workPlace}
            </WrappedText>
          )}
        </BoxContainer>
      </BoxContainer>

      <LocalizedContentParser
        className='px-4 pt-1 text-fg leading-8'
        renderWrapper
        parentSectionId={`${slug?.current}_testimonial_section`}
      >
        {profile?.textContent}
      </LocalizedContentParser>

      <BoxContainer className='px-4 pb-4'>
        {profile?.linkUrl && (
          <Badge className='w-fit' variant='solid' colorPalette='brand'>
            <NextLink
              href={profile?.linkUrl ?? ''}
              linkLabel={profile?.linkText}
              hideExternalIcon
              className='flex gap-1'
            >
              {profile?.linkText ?? extractSiteName(profile?.linkUrl)}
              <LaunchIcon opacity={0.65} width={17.5} height={17.5} />
            </NextLink>
          </Badge>
        )}
      </BoxContainer>
    </BoxContainer>
  )
}
