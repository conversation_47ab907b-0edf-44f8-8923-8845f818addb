import { Fragment } from 'react'
import { BoxContainer } from '../components/common/BoxContainer'
import { ComingSoonImageLink } from '../components/common/ComingSoonImageLink'
import { ImageLink } from '../components/common/ImageLink'
import { SanityImage } from '../components/common/SanityImage'
import { LocalizedContentParser } from '../components/typography/LocalizedContentParser'
import { Title } from '../components/typography/Title'
import {
  convertToPlainText,
  replaceWhitespace,
} from '../helper-functions/UtilFunctions'
import { STYLE } from '../styles/style'
import { AppDownloadEvent } from '../types/Analytics/AnalyticsEvents.types'
import type { AnalyticsObjectIds } from '../types/Analytics/AnalyticsObjectIds.types'
import type { LinkWithImageType, SanityImageType } from '../types/common.types'
import type { SharedSectionDataType } from '../types/sections/section.types'

type DownloadSectionProps = {
  sectionData: {
    sectionImage?: SanityImageType
    appLinks?: Array<LinkWithImageType>
  } & SharedSectionDataType
}

/** Download Section component is a section that contains a title, description, image, and a pair of app store badges.
 */
export function DownloadSection({ sectionData }: DownloadSectionProps) {
  const convertedTitle = convertToPlainText({ value: sectionData?.title }) ?? ''

  const slug =
    sectionData?.slug?.current ??
    replaceWhitespace({ str: convertedTitle }).toLowerCase()
  const headId: string = `head-${slug}`

  const objectId: AnalyticsObjectIds = `${sectionData?.slug?.current}_download_section`
  return (
    <BoxContainer
      aria-label={convertedTitle}
      aria-labelledby={headId}
      className='bg-background py-6'
      as={'section'}
      id={slug}
    >
      <BoxContainer className='mx-auto flex w-[80%] flex-col items-center justify-between gap-5 lg:flex-row lg:gap-10 xl:gap-0'>
        <BoxContainer className='flex w-[80%] flex-col gap-5 xl:w-[50%]'>
          <Title
            as={'h2'}
            id={headId}
            className='font-bold text-4xl text-fg sm:text-5xl md:text-6xl lg:text-5xl xl:text-5xl'
          >
            {sectionData?.title}
          </Title>

          {sectionData?.subtitle && (
            <LocalizedContentParser
              renderWrapper
              className='w-fit text-lg leading-7.5 xl:text-xl'
              parentSectionId={objectId}
            >
              {sectionData?.subtitle}
            </LocalizedContentParser>
          )}

          <BoxContainer className='justify center mb-5 flex flex-col flex-wrap items-center justify-start gap-5 sm:gap-3 md:mb-0 lg:flex-row lg:gap-5'>
            {sectionData?.appLinks?.map((appLink, index) => (
              <Fragment key={`app-link-${index}`}>
                {appLink?.comingSoon ? (
                  <ComingSoonImageLink
                    linkImage={appLink?.linkImage}
                    comingSoonProps={{
                      className:
                        'absolute -top-[0.9rem] -right-[0.5rem] z-110 h-min w-max rounded-sm bg-golden-yellow px-1 py-0.5 text-[clamp(0.75rem,0.4647rem+0.0941vw,0.7rem)] font-semibold text-fg-700 shadow-sm select-none sm:-top-[1rem]',
                    }}
                  />
                ) : (
                  <ImageLink
                    key={index}
                    {...appLink}
                    customEvent={AppDownloadEvent.download}
                    parentSectionId={objectId}
                  />
                )}
              </Fragment>
            ))}
          </BoxContainer>
        </BoxContainer>

        <SanityImage
          src={sectionData?.sectionImage?.url}
          alt={sectionData?.sectionImage?.altText}
          skeletonProps={{
            className: 'h-50 w-75 sm:h-75 sm:w-100 md:h-[350px] md:w-[500px]',
          }}
          fillProp
          sizes={STYLE.DOWNLOAD_SECTION_IMAGE_OPTIMIZATION}
        />
      </BoxContainer>
    </BoxContainer>
  )
}
