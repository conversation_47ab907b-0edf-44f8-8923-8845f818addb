import type {
  DownloadFileData,
  PageDomainType,
  SanityImageType,
  SlugArray,
} from './common.types'
import type { SectionType } from './sections/section.types'
import type { SharedPageData } from './shared-page-data.types'
import type { WebSchemasWebsiteWideProps } from './webschema.types'

type PageSeo = {
  seoDescription: string
  pageTitle: string
  seoImage: SanityImageType
  seoKeywords: Array<string>
}

type PageSlug = {
  current: string
}
type HomePageData = {
  webschemasOnWebsite: Array<WebSchemasWebsiteWideProps>
  homepage?: {
    pageSections?: Array<SectionType>
    pageSlug?: PageSlug
    pageSeo?: PageSeo
  }
  pageSections: {
    sectionSlug: string
    featuredSectionImage: DownloadFileData
  }
} & PageDomainType

type DynamicPageData = {
  pageSlug?: PageSlug
  pageSubtitle?: string
  pageSections?: Array<SectionType>
  pageSeo?: PageSeo
  pagesOnWebsite?: {
    pageSections?: Array<SectionType>
    pageSlug?: PageSlug
    pageSeo?: PageSeo
  }
}

type PagesOnWebsite = Array<{
  pageSlug: string
  slugArray?: SlugArray
  updatedAt: string
}>

type StaticPageParamData = {
  pagesOnWebsite: PagesOnWebsite
}

type PageWithImages = {
  pagesOnWebsite: Array<{
    slug: string
    images: Array<string>
  }>
}

type UpdatedAtData = {
  pageSlug?: string
  articleSlug?: string
  videoSlug?: string
  researchSlug?: string
  articleUpdatedAt?: string
  videoUpdatedAt?: string
  pageUpdatedAt?: string
  researchUpdatedAt?: string
  articleTitle: string
  articlePublisher: string
}

type PagesData = HomePageData & DynamicPageData & UpdatedAtData & SharedPageData

export type {
  DynamicPageData,
  HomePageData,
  PagesData,
  PageSeo,
  PagesOnWebsite,
  PageWithImages,
  StaticPageParamData,
}
