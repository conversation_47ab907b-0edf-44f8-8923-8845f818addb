import type { BoxContainerProps } from '../../components/common/BoxContainer'
import type { GenericButtonProps } from '../../components/common/GenericButton'

type SelectOptionType<T = string> = {
  value: T
  label: React.ReactNode
}

type SelectOptionSelectType<T> = {
  handleSelect: (option: SelectOptionType<T>) => void
}

type SelectOptionProps<T> = {
  option: SelectOptionType<T>
  buttonProps?: GenericButtonProps
} & SelectOptionSelectType<T>

type SelectOptionListProps<T> = {
  options: Array<SelectOptionType<T>>
  buttonProps?: GenericButtonProps
} & BoxContainerProps &
  SelectOptionSelectType<T>

type SelectProps<T = string> = {
  options: Array<SelectOptionType<T>>
  defaultOption?: SelectOptionType<T>
  placeholder?: string
  clearable?: boolean
  selectListProps?: BoxContainerProps
  setSelectedValue?: (value: T) => void
  triggerProps?: GenericButtonProps
  buttonProps?: GenericButtonProps
  hideChevron?: boolean
} & BoxContainerProps

export type {
  SelectOptionListProps,
  SelectOptionProps,
  SelectOptionType,
  SelectProps,
}
