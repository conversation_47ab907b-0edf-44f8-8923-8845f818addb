import type { LinkProps } from 'next/link'
import type { HTMLAttributes } from 'react'

import type { BoxContainerProps } from '../../components/common/BoxContainer'
import type { SanityImageProps } from '../../components/common/SanityImage'
import type { AnalyticsEvents } from '../Analytics/Analytics.types'
import type { AnalyticsObjectIds } from '../Analytics/AnalyticsObjectIds.types'
import type {
  AdditionalComponentAttributes,
  SanityImageType,
} from '../common.types'
import type { IconProps } from '../component.types'
import type {
  LocalizedStringContentType,
  ParentSectionID,
} from '../sections/section.types'

type NextLinkTrackingProps = {
  href: string
  linkLabel?: string
  customEvent?: AnalyticsEvents
  objectId?: AnalyticsObjectIds
  customValue?: unknown
  trackHover?: boolean
  customHoverEvent?: AnalyticsEvents
  onClick?: () => void
} & AdditionalComponentAttributes

type NextLinkProps = {
  children: React.ReactNode
  href: string
  className?: string
  hideExternalIcon?: boolean
  externalIconProps?: IconProps
} & NextLinkTrackingProps &
  LinkProps &
  HTMLAttributes<HTMLAnchorElement>

type LinkWithImageProps = {
  href: string
  linkImage: SanityImageType
  comingSoon?: boolean
  linkTitle?: LocalizedStringContentType
  customEvent?: AnalyticsEvents
  parentSectionId: ParentSectionID['parentSectionId']
  imageProps?: SanityImageProps
  linkProps?: NextLinkProps
  comingSoonProps?: BoxContainerProps
} & Omit<BoxContainerProps, 'title'>

export type { LinkWithImageProps, NextLinkProps, NextLinkTrackingProps }
