import type { PersonType, SanityImageType } from '../common.types'
import type {
  LocalizedContentType,
  ParentSectionID,
  SharedSectionDataType,
} from './section.types'

type ContentPost = {
  _type: string
  _createdAt?: string
  _updatedAt?: string
  authors?: Array<PersonType>
  postImage: SanityImageType
  body: LocalizedContentType
  publishDate?: string
  readingTime?: number

  videoThumbnail?: SanityImageType

  videoFile?: {
    playbackId: string
    duration: number
    quality: string
    updatedAt: string
    createdAt: string
    height: number
    width: number
  }

  manuscript?: {
    asset: {
      url: string
      originalFilename: string
    }
  }

  seoKeywords?: Array<string>
} & SharedSectionDataType

type ContentPostProps = {
  postTitle?: LocalizedContentType
  postSummary?: LocalizedContentType
  authors?: Array<PersonType>
  postImage: string
  postBodyMarkdown?: LocalizedContentType
  videoId?: string
  postDate?: string
  postPublishImageUrl?: string
  postReadTimeImageUrl?: string
  postReadTime?: string
  researchPdfFile?: string
  featuredPost?: boolean
  videoThumbnail?: string
  videoDuration?: number
  parentSlug?: string
  slug?: string
  children?: React.ReactNode
} & ParentSectionID

type ContentSectionType = {
  disclaimer: string
  postType: string
  featuredPost: ContentPost
  postsArray: Array<ContentPost>
} & SharedSectionDataType

type ContentPostFeedData = {
  pagesOnWebsite: Array<{
    pageSlug: string
    subPages?: Array<{
      title: string
      subtitle: string
      updatedAt: string
      slug: string
      postImageUrl: string
      videoFile: {
        playbackId: string
        duration: number
        height: number
        width: number
      }
    }>
  }>
}

export type {
  ContentPost,
  ContentPostFeedData,
  ContentPostProps,
  ContentSectionType,
}
