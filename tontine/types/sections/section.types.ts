import type { PortableTextBlock } from 'next-sanity'

import type { sections } from '../../sections/Sections'
import type { AnalyticsEvents } from '../Analytics/Analytics.types'
import type { AnalyticsObjectIds } from '../Analytics/AnalyticsObjectIds.types'
import type {
  LanguagesType,
  MuxVideoType,
  PageDomainType,
  PersonType,
  SanityButton,
  SanityImageType,
  SlugType,
} from '../common.types'
import type { SubMenuItem } from '../shared-page-data.types'
import type { ContentSectionType } from './content-section.types'
import type { CTACardSectionType } from './cta.types'
import type { FaqSectionType } from './faq-section.types'
import type { CarouselHeroSectionType } from './hero-types'
import type {
  InfoBannerSectionType,
  InfoBlockSectionType,
} from './info-section.types'
import type { PartnerSectionType } from './partner-section.types'

type SectionMediaType = {
  sectionImage?: SanityImageType
  sectionVideo?: MuxVideoType
  hideControls?: boolean
  videoThumbnail?: SanityImageType
}

type LocalizedContentType = {
  [K in LanguagesType]?: Array<PortableTextBlock>
}

type LocalizedStringContentType = {
  [K in LanguagesType]?: string
}

type LocalizedContentHeader = {
  title?: LocalizedContentType
  subtitle?: LocalizedContentType
}

type LocalizedStringHeader = {
  stringTitle?: LocalizedStringContentType
  stringSubtitle?: LocalizedStringContentType
}
type SharedSectionDataType = {
  shareEvent?: AnalyticsEvents
  slug: SlugType
  icon?: SanityImageType
} & LocalizedContentHeader &
  LocalizedStringHeader

type FeaturedSectionType = {
  featuredSectionDescription?: string
  isHero?: boolean
  featuredSectionMediaDirectionToggle?: boolean
  featuredSectionImage?: SanityImageType
  featuredSectionButtons?: Array<SanityButton>
  featuredSectionEllipticalImageToggle?: boolean
}

type ContactUsSectionType = {
  placeholderEmailInput: string
  placeholderNameInput: string
  placeholderSelectInput: string
  placeholderSurnameInput: string
  selectInputOptions: Array<string>
  placeholderTextareaInput: string
  submitButtonLabel: string
  disclaimer: string
}

type ParentSectionID = {
  parentSectionId?: AnalyticsObjectIds
}

type SectionType = {
  _type: string

  faqButton?: SanityButton
  placeholderText: LocalizedStringContentType
  localeDisclaimer: LocalizedContentType
  pageSections: SubMenuItem

  teamMembers: Array<PersonType>
  testimonialPosts: Array<TestimonialType>

  width: number
  height: number

  _updatedAt?: string
} & SharedSectionDataType &
  ContentSectionType &
  CarouselHeroSectionType &
  AboutUsSectionType &
  PartnerSectionType &
  InfoBlockSectionType &
  CTACardSectionType &
  InfoBannerSectionType &
  FaqSectionType &
  FeaturedSectionType &
  ContactUsSectionType &
  TestimonialCarouselSectionType &
  CompaniesLogoType &
  PageDomainType

type AboutUsSectionType = {
  aboutUsSectionContent?: string
  aboutUsSectionButtons?: SanityButton
  aboutUsSectionEmailIcon?: SanityImageType
  aboutUsSectionEmail?: string
  aboutUsSectionPhoneNumber?: string
  aboutUsSectionPhoneIcon?: SanityImageType
  aboutUsSectionAddress?: string
  aboutUsSectionAddressIcon?: SanityImageType
  aboutUsSectionSubtitleCaption: string
}

type TestimonialType = {
  testimonialPostReviewerName: string
  testimonialReviewerProfession?: string
  testimonialReviewerCompany?: string
  reviewerLinkText?: string
  reviewerLinkUrl?: string
  localizedTestimonialPost: LocalizedContentType
  testimonialReviewerImage: SanityImageType
}

type TestimonialCardType = {
  name: string
  profession?: string
  workPlace?: string
  linkText?: string
  linkUrl?: string
  textContent: LocalizedContentType
  image: SanityImageType
}

type TestimonialCarouselPost = {
  carouselReviewerDescription: string
  carouselPostReviewerName: string
  carouselPostReviewerImage: SanityImageType
} & SharedSectionDataType

/** Carousel Section types eg. the one About US */
type TestimonialCarouselSectionType = {
  localizedCarouselSectionFeedback?: LocalizedContentType
  carouselPosts?: Array<TestimonialCarouselPost>
}
type CompaniesLogoType = { imageField: SanityImageType }

type AllSections = typeof sections

// Use the keyof type operator to create a type representing all possible keys of the sections object
type AllSectionKeys = keyof AllSections

export type {
  AboutUsSectionType,
  AllSectionKeys,
  CompaniesLogoType,
  ContactUsSectionType,
  LocalizedContentHeader,
  LocalizedContentType,
  LocalizedStringContentType,
  LocalizedStringHeader,
  ParentSectionID,
  SectionMediaType,
  SectionType,
  SharedSectionDataType,
  TestimonialCardType,
  TestimonialCarouselPost,
  TestimonialCarouselSectionType,
  TestimonialType,
}
