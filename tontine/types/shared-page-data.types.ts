import type {
  LinkCustomType,
  LinkSelectorProps,
  PageDomainType,
  SanityImageType,
  SlugType,
  SocialMediaIcon,
} from './common.types'
import type { PageSeo } from './pages.types'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
  SharedSectionDataType,
} from './sections/section.types'

type FooterDataType = {
  footerLinks: Array<LinkCustomType>
  title: string
  footerSocialMediaSection: Array<{
    title: string
    socialMediaSectionItems: Array<SocialMediaIcon>
  }>
  footerNavigationMenu: Array<NavBarNavigationMenuItem>
  footerWebsiteLogo: SanityImageType
  localizedTontineCopyrightContent: LocalizedContentType
  slug: string
}

type SharedPageData = {
  siteName: string
  pageSeo?: PageSeo
  openGraphType: string
  twitterCreator: string
  twitterSite: string
  twitterCardType: string
  navigationMenu?: NavBarNavigationMenuType
  websiteFeedbackModal?: FeedbackType
  footer?: FooterDataType
  websiteFavicon?: SanityImageType
  _updatedAt?: string
} & PageDomainType

type NavBarNavigationMenuItem = {
  navigationSubMenuItems?: Array<SubMenuItem>
  navItemSlug: string
  onToggle?: () => void
} & LinkSelectorProps &
  SharedSectionDataType

type NavBarNavigationMenuType = {
  stringTitle: string
  stringSubtitle: string
  websiteLogo: SanityImageType
  navigationItems?: Array<NavBarNavigationMenuItem>
  hideAuthButtons: boolean
  loginLink?: SlugType
  registerLink?: SlugType
  languagePickerVisibility: boolean
}

type WebsiteIdsArrayType = {
  websiteIds: Array<{
    id: string
    title: string
  }>
}

type NavigationMenuDataProps = {
  sharedPageData?: SharedPageData & WebsiteIdsArrayType
  navigationMenuData?: NavBarNavigationMenuType
  feedbackData?: FeedbackType
  inProp?: boolean
  onToggle?: () => void
}

type SubMenuItem = {
  icon: SanityImageType
  subSlug: string
  subMenuItemNew?: boolean
  subMenuItemComingSoon?: boolean
} & LinkSelectorProps &
  Omit<SharedSectionDataType, 'slug'>

type FeedbackType = {
  ctaButtonLabel: LocalizedStringContentType
  failedFeedbackTitle: LocalizedStringContentType
  failedFeedbackDescription?: LocalizedStringContentType
  feedbackModalTitle: LocalizedStringContentType
  highestRatingText: LocalizedStringContentType
  lowestRatingText: LocalizedStringContentType
  submitButtonLabel: LocalizedStringContentType
  redirectButtonLabel: LocalizedStringContentType
  successfulFeedbackTitle: LocalizedStringContentType
  successfulFeedbackDescription?: LocalizedStringContentType
  textPlaceholder: LocalizedStringContentType
  excludePages?: Array<string>
}

export type {
  FeedbackType,
  NavBarNavigationMenuItem,
  NavBarNavigationMenuType,
  NavigationMenuDataProps,
  SharedPageData,
  SubMenuItem,
  WebsiteIdsArrayType,
}
