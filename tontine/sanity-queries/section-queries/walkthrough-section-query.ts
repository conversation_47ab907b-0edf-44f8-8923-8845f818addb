const walkthroughPins = (deviceType: string) => {
  return /* groq */ `*[_type == "walkthroughPin" && references(^._id)]{
        _id,
        title,
        subtitle,
        "placement": {
        "column": placement.${deviceType}.column,
        "row": placement.${deviceType}.row
        },
    },`
}

const walkthroughGridImage = /* groq */ `
defined(walkthroughGridImage)=>{
    "walkthroughGridImage":{
        "altText": walkthroughGridImage.asset->altText,
        "url": walkthroughGridImage.asset->url,
    },
},
`

export const walkthroughSectionQuery = /* groq */ `
defined(desktopWalkthroughs)=>{
    "desktopWalkthroughs": desktopWalkthroughs[]->{
        ${walkthroughGridImage}
        "walkthroughPins": ${walkthroughPins('desktop')}
    },
},
defined(mobileWalkthroughs)=>{
    "mobileWalkthroughs": mobileWalkthroughs[]->{
        ${walkthroughGridImage}
        "walkthroughPins": ${walkthroughPins('mobile')}
    },
},
`
