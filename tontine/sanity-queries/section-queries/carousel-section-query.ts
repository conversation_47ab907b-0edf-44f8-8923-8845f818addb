import { sharedSectionDataQuery } from '../common-type-queries/common-types-queries'

// This query is used to fetch carousel section data from sanity
const carouselSectionQuery: string = /* groq */ `
defined(carouselPosts)=>{
  "carouselPosts" : carouselPosts[]{
    ${sharedSectionDataQuery}
    defined(carouselPostReviewerName) => {carouselPostReviewerName},
    defined(carouselReviewerDescription) => {carouselReviewerDescription},
    defined(carouselPostReviewerImage)=>{
      "carouselPostReviewerImage":{
        "altText": carouselPostReviewerImage.asset->altText,
        "url": carouselPostReviewerImage.asset->url,
      },
    },
  },
},
defined(localizedCarouselSectionFeedback) => {localizedCarouselSectionFeedback},
`
export { carouselSectionQuery }
