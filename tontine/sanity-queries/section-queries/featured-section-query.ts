import { sectionMediaQuery } from '../common-type-queries/common-types-queries'
import { linkSelectorQuery } from '../miscellaneous-queries/miscellaneous-queries'

// Query that fetches data from sanity for the featured section..
const featuredSectionQuery: string = /* groq */ `
defined(isHero) => {isHero},
defined(featuredSectionMediaDirectionToggle) => {featuredSectionMediaDirectionToggle},
defined(featuredSectionEllipticalImageToggle) => {featuredSectionEllipticalImageToggle},
${sectionMediaQuery}
defined(featuredSectionElementCollection) => {featuredSectionElementCollection},
defined(featuredSectionButtons)=>{
  "featuredSectionButtons":featuredSectionButtons[]->{
    "buttonLabel": stringTitle,
      ${linkSelectorQuery},
  },
},
defined(featuredSectionSubscribeToggle) => {featuredSectionSubscribeToggle},
`
export { featuredSectionQuery }
