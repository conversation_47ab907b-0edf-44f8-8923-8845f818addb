import { linkWithImageQuery } from '../miscellaneous-queries/miscellaneous-queries'

// Query that fetches data from sanity for the download section..
const downloadSectionQuery: string = /* groq */ `
defined(sectionImage)=>{
  "sectionImage":{
    "altText": sectionImage.asset->altText,
    "url": sectionImage.asset->url,
  },
},
defined(appLinks)=>{
  appLinks[]->{
    ${linkWithImageQuery}
  }
},
`
export { downloadSectionQuery }
