// This query is used to fetch image from <PERSON><PERSON> which is used as favicon for the website.
const websiteFavIconQuery: string = /* groq */ `
defined(websiteFavicon)=>{
  "websiteFavicon":{
    "altText": websiteFavicon.asset->altText,
    "url": websiteFavicon.asset->url,
  },
}
`
const seoImageQuery: string = /* groq */ `
defined(seoImage)=>{
  "seoImage": {
    "altText": seoImage.asset->altText,
    "url": seoImage.asset->url,
  },
}
`

const seoDataQuery = /* groq */ `
*[_id == $WEBSITE_TONTINE][0]{
  pageDomain,
  siteName,
  twitterCreator,
  twitterSite,
  twitterCardType,
  openGraphType,
  ${websiteFavIconQuery},
}
`

const portableDataQuery = /*groq*/ `
...,
children[]{
  ...,
  _type == "image" => {
    asset->{
      url,
      "aspectRatio": metadata.dimensions.aspectRatio,
      altText
    }
  },
},
markDefs[]{
  ...,
  _type == "tooltipWrapper" => {
    type,
    textTooltip {
      ...tooltipText
    },
    glossaryTooltip {
      ...reference->{
        slug,
        "title": stringTitle,
        "subtitle": localeMarkdown,
      }
    }
  }
}
`

const localizedPortableTextQuery = /*groq*/ `
en[]{
  ${portableDataQuery}
},
es[]{
  ${portableDataQuery}
},
pt[]{
  ${portableDataQuery}
},
`

// Query used for the header data across all sections. Each section uses different sets of fields
const sharedSectionDataQuery = /* groq */ `
_type,
defined(slug)=>{slug},
defined(localizedSubtitle)=>{
  "subtitle": localizedSubtitle {${localizedPortableTextQuery}},
},
defined(localizedTitle)=>{
  "title": localizedTitle {${localizedPortableTextQuery}},
},
defined(stringTitle)=>{stringTitle},
defined(stringSubtitle)=>{stringSubtitle},
defined(icon)=>{
  "icon": {
    "altText": icon.asset->altText,
    "url": icon.asset->url,
  },
},
`

const sectionMediaQuery = /* groq */ `
defined(sectionVideo) => {
  "sectionVideo": sectionVideo.asset->{
    defined(playbackId)=>{"playbackId": playbackId},
    defined(data.duration)=>{"duration": data.duration},
    defined(data.max_stored_resolution)=>{"quality": data.max_stored_resolution},
    defined(data.tracks)=>{"width": data.tracks[max_width != null].max_width},
    defined(data.tracks)=>{"height": data.tracks[max_width != null].max_height},
    defined(_updatedAt)=>{"updatedAt": _updatedAt},
    defined(_createdAt)=>{"createdAt": _createdAt},
  },
},
defined(hideControls) => {hideControls},
defined(videoThumbnail) => {
  "videoThumbnail":{
    "url": videoThumbnail.asset->url,
    "altText": videoThumbnail.asset->altText,
  },
},
defined(sectionImage)=>{
  "sectionImage":{
    "altText": sectionImage.asset->altText,
    "url": sectionImage.asset->url,
  },
},
`

export {
  localizedPortableTextQuery,
  portableDataQuery,
  sectionMediaQuery,
  seoDataQuery,
  seoImageQuery,
  sharedSectionDataQuery,
}
