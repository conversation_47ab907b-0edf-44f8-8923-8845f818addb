import { sharedSectionDataQuery } from '../common-type-queries/common-types-queries'
import { linkSelectorQuery } from '../miscellaneous-queries/miscellaneous-queries'

// Query that fetches data from Sanity for our footer on the website.
const footerQueryOnWebsite: string = /* groq */ `
defined(title) => {title},
defined(footerWebsiteLogo)=>{
  "footerWebsiteLogo":{
    "altText": footerWebsiteLogo.asset->altText,
    "url": footerWebsiteLogo.asset->url,
  },
},
defined(slug)=>{
  "slug": slug.current,
},
footerSocialMediaSection[]->{
defined(title) => {title},
socialMediaSectionItems[]->{
    defined(icon)=>{
      "icon":{
          "altText": icon.asset->altText,
          "url": icon.asset->url,
      },
    },
    defined(url) => {url},
    defined(title) => {title},
},
},
footerNavigationMenu[]->{
  ${sharedSectionDataQuery}
  ${linkSelectorQuery},
  navigationSubMenuItems[]->{
    ${linkSelectorQuery},
    ${sharedSectionDataQuery}
    defined(subMenuItemComingSoon) => {subMenuItemComingSoon},
  },
},
footerLinks[]->{
defined(linkLabel) => {linkLabel},
${linkSelectorQuery},
},
defined(localizedTontineCopyrightContent) => {localizedTontineCopyrightContent},
`
export { footerQueryOnWebsite }
