// Query that fetches data from sanity for the navigation menu on the website.

import { sharedSectionDataQuery } from '../common-type-queries/common-types-queries'
import { linkSelectorQuery } from '../miscellaneous-queries/miscellaneous-queries'

// This query fetches nav items and sub nav items, so the numbers of nav items and sub nav items are dynamic.
const navigationMenuOnWebsiteQuery: string = /* groq */ `
defined(title) => {title},
defined(websiteLogo)=>{
  "websiteLogo":{
        "altText": websiteLogo.asset->altText,
        "url": websiteLogo.asset->url,
    },
    },
  navigationItems[]->{
    stringTitle,
    stringSubtitle,
    ${linkSelectorQuery},
    navigationSubMenuItems[]->{
      ${linkSelectorQuery},
      ${sharedSectionDataQuery}
      defined(icon)=>{
          "icon": {
            "altText": icon.asset->altText,
            "url": icon.asset->url,
        },
      },
      defined(subMenuItemNew) => {subMenuItemNew},
      defined(subMenuItemComingSoon) => {subMenuItemComingSoon},
  },
},
defined(authButtonsVisibility)=>{"hideAuthButtons": authButtonsVisibility},
defined(loginLink)=>{loginLink},
defined(registerLink)=>{registerLink},
defined(languagePickerVisibility)=>{languagePickerVisibility}, 
`
export { navigationMenuOnWebsiteQuery }
