import { sharedSectionDataQuery } from '../common-type-queries/common-types-queries'

// Query that's part of the partner section query. This query it's used to fetch data from Sanity which is used in partner section. And it returns data for a single partner.
const partnerQuery: string = /* groq */ `
${sharedSectionDataQuery}
defined(partnerImage)=>{
"partnerImage":{
      "altText": partnerImage.asset->altText,
      "url": partnerImage.asset->url,
},
},
defined(partnerExternalLink) => {partnerExternalLink},
`

const linkSelectorQuery = /* groq */ `
defined(linkType) => {linkType},
defined(allPages) => {"pageSlug": allPages->.pageSlug},
defined(allPageSections) => {"sectionSlug": allPageSections->.slug},
defined(customLink) => {customLink},
defined(customParamsButton) => {customParamsButton},
defined(customParams) => {customParams}
`

// Query that's part of the info block section query.
const infoBlockQuery: string = /* groq */ `
${sharedSectionDataQuery}
defined(icon)=>{
  "infoBlockImage":{
    "altText": icon.asset->altText,
    "url": icon.asset->url,
  },
},
defined(infoBlockButtons)=>{
  "infoBlockButtons": infoBlockButtons[]->{
  defined(stringTitle) => {"buttonLabel": stringTitle},
    ${linkSelectorQuery},
  },
},
`

// Query used to fetch data for a single person, from Sanity. This query is used in the team section query.
const personQuery: string = /* groq */ `
defined(personImage)=>{
  "personImage":{
    "altText": personImage.asset->altText,
    "url": personImage.asset->url,
  },
},
defined(personAlternativeImage)=>{
  "personAlternativeImage":{
    "url": personAlternativeImage.asset->url,
  },
},
defined(personName) => {personName},
defined(personDescription) => {personDescription},
defined(personTitle) => {personTitle},
personSocialLink->{
  defined(icon)=>{
    "icon":{
      "altText": icon.asset->altText,
      "url": icon.asset->url,
    },
  },
  defined(url) => {url},
},
`

const manifestQuery = /* groq */ `
*[_id == $WEBSITE_TONTINE][0]{
  "description": webschemasOnWebsite[@->_type == "corporationSchema"][0]->corporationSchemaDescription,
  "name": webschemasOnWebsite[@->_type == "corporationSchema"][0]->corporationSchemaName,
  pageDomain,
  "shortName": siteName,
}
`

const rssFeedQuery = /* groq */ `
*[_id == $WEBSITE_TONTINE][0]{
  "description": webschemasOnWebsite[@->_type == "corporationSchema"][0]->corporationSchemaDescription,
  "shortName": siteName,
  "updatedAt": _updatedAt,
}
`

const contentQuery = /* groq */ `
{
  "slug": slug.current, 
  "postImageUrl": postImage.asset->url, 
  "videoFile": videoFile.asset->{
    "playbackId": playbackId,
    "duration": data.duration,
    "width": data.tracks[max_width != null].max_width,
    "height": data.tracks[max_width != null].max_height,
  },
  "updatedAt": _updatedAt,  title, subtitle
}
`

const rssContentQuery = /* groq */ `
*[_id == $WEBSITE_TONTINE][0].pagesOnWebsite[]->{
  "pageSlug": pageSlug.current,
  "subPages":array::compact(pageSections[]->.postsArray[]->${contentQuery}) + array::compact(pageSections[]->.featuredPost[]->${contentQuery})
}`

const allImagesQuery = /* groq */ `
*[_id == $WEBSITE_TONTINE][0]{
  "pagesOnWebsite":
    array::compact(
      [
        homepage->,
        ...pagesOnWebsite[]->,
      ]{
        "slug": pageSlug.current,
        "images": array::unique(
          array::compact(
            pageSections[]->{
              "combinedImages": [
                sectionImage.asset->url,
                videoThumbnail.asset->url,
                icon.asset->url,
                ...infoHubCardList[]->.icon.asset->url,
                ...testimonialPosts[].testimonialReviewerImage.asset->url,
                ...carouseHeroSectionPosts[]->.sectionImage.asset->url,
                ...carouselPosts[].carouselPostReviewerImage.asset->url,
                ...partnersCompanies[]->.partnerImage.asset->url,
                ...companiesLogosIcon[]->.imageField.asset->url,
                ...teamMembers[]->.personImage.asset->url,
                ...teamMembers[]->.personAlternativeImage.asset->url,
                ...featuredPost[]->.postImage.asset->url,
                ...postsArray[]->.postImage.asset->url,
              ]
            }.combinedImages[]
          )
      )
    }
  )
}
`

const feedbackQuery = /* groq */ `
websiteFeedbackModal->{
  "ctaButtonLabel": localizedCtaButtonLabel,
  "feedbackModalTitle": localizedFeedbackModalTitle,
  "feedbackModalDescription": localizedFeedbackModalDescription,
  "lowestRatingText": localizedLowestRatingText,
  "highestRatingText": localizedHighestRatingText,
  "successfulFeedbackTitle": localizedSuccessfulFeedbackTitle,
  "successfulFeedbackDescription": localizedSuccessfulFeedbackDescription,
  "failedFeedbackTitle": localizedFailedFeedbackTitle,
  "failedFeedbackDescription": localizedFailedFeedbackDescription,
  "textPlaceholder": localizedTextPlaceholder,
  "submitButtonLabel": localizedSubmitButtonLabel,
  "redirectButtonLabel": localizedRedirectButtonLabel,
  defined(excludePages) => {
    "excludePages": excludePages[]->.pageSlug.current
  },
},
`

const pagesQuery = /* groq */ `
"pageSlug": pageSlug.current,
"updatedAt": _updatedAt,
"slugArray": array::compact(pageSections[]->.postsArray[]->{"slug": slug.current, "updatedAt": _updatedAt, "title": localizedTitle}) + array::compact(pageSections[]->.featuredPost[]->{"slug": slug.current, "updatedAt": _updatedAt, "title": localizedTitle}),
`
const linkWithImageQuery = /* groq */ `
${sharedSectionDataQuery}
defined(href) => {href},
defined(linkImage) => {
  "linkImage": {
    "altText": linkImage.asset->altText,
    "url": linkImage.asset->url,
  },
},
defined(comingSoon) => {
  comingSoon
},
`

export {
  allImagesQuery,
  feedbackQuery,
  infoBlockQuery,
  linkSelectorQuery,
  linkWithImageQuery,
  manifestQuery,
  pagesQuery,
  partnerQuery,
  personQuery,
  rssContentQuery,
  rssFeedQuery,
}
