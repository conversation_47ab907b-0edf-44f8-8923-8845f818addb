import type { QueryParams } from 'next-sanity'
import { client } from 'sanityClient'

import { PageSEO } from '../components/page-seo/PageSEO'
import {
  convertToPlainText,
  generatePathWithTrailingSlash,
} from '../helper-functions/UtilFunctions'
import { logServerless } from '../serverless/ApiUtilFunctions'
import { defaultWebsiteId, isProd, prodWebsiteID } from '../serverless/keys'
import type { PageDomainType } from '../types/common.types'
import type {
  ContentPost,
  ContentSectionType,
} from '../types/sections/content-section.types'
import type { SharedPageData } from '../types/shared-page-data.types'
import { seoDataQuery } from './common-type-queries/common-types-queries'
import { moreLikeThisQuery } from './content-post-queries/content-post-queries'
import { contentPageQuery, sharedPageDataQuery } from './sanity-queries'

/**
 * Fetches data from Sanity based on the given query and params.
 *
 * `query` - The Sanity query to be executed.
 * `params` - The query parameters to be used.
 */
async function fetchSanityData<QueryResponse>({
  query,
  params = {},
  websiteIdCookie,
}: {
  query: string
  params?: QueryParams
  websiteIdCookie?: string
}): Promise<QueryResponse> {
  try {
    const websiteTontine = isProd
      ? (prodWebsiteID ?? defaultWebsiteId)
      : (websiteIdCookie ?? defaultWebsiteId)

    const defaultParams = {
      WEBSITE_TONTINE: websiteTontine,
      pageType: 'page',
    }
    const mergedParams = { ...defaultParams, ...params }

    return await client.fetch<QueryResponse>(query, mergedParams)
  } catch (error) {
    logServerless({
      logLevel: 'error',
      error,
      message: `fetchSanityData() - Failed to fetch sanity data. Query: ${query}`,
    })
    throw error
  }
}

async function getContentPageData({
  parentSlug,
  childSlug,
}: {
  parentSlug: string
  childSlug: string
}) {
  type ContentPageData = {
    pageSections?: Array<
      ContentSectionType & {
        posts: ContentPost
      }
    >
    pageSeo: {
      seoKeywords: Array<string>
    }
  } & PageDomainType
  const data = await fetchSanityData<ContentPageData>({
    query: contentPageQuery,
    params: { parentSlug, childSlug },
  })

  const contentPageSections = data?.pageSections?.filter(
    (item) => item.featuredPost !== undefined || item.posts !== undefined
  )

  const featuredPost = contentPageSections?.[0]?.featuredPost

  const regularPosts = contentPageSections?.[0]?.posts

  if (featuredPost) {
    return {
      pageData: featuredPost,
      keywords: featuredPost?.seoKeywords ?? data?.pageSeo?.seoKeywords,
      isFeatured: true,
      pageDomain: data?.pageDomain,
    }
  }
  return {
    pageData: regularPosts,
    keywords: regularPosts?.seoKeywords ?? data?.pageSeo?.seoKeywords,
    isFeatured: false,
    pageDomain: data?.pageDomain,
  }
}

/** Fetches SEO data for pages */
const fetchPageSEOData = async ({
  slug,
  preview,
}: {
  slug: Array<string> | null
  preview: boolean
}) => {
  if (preview) {
    return {
      title: 'Preview Page Data',
      description: 'Placeholder metadata for preview calls.',
    }
  }

  // Used for content pages
  if (slug && slug?.length > 1 && slug[0] && slug[1]) {
    const parentSlug = slug[0]
    const childSlug = slug[1]
    const { pageData, keywords } = await getContentPageData({
      parentSlug,
      childSlug,
    })

    const sharedPageData = await fetchSanityData<SharedPageData>({
      query: seoDataQuery,
    })

    if (!pageData || !sharedPageData)
      throw new Error('Could not find any SEO page data.')

    return PageSEO({
      seoImage: pageData?.postImage?.url,
      seoTitle: convertToPlainText({ value: pageData?.title }),
      seoDescription: convertToPlainText({ value: pageData?.subtitle }),
      seoImageAlt: pageData?.postImage?.altText,
      pageDomain: sharedPageData?.pageDomain,
      siteName: sharedPageData?.siteName,
      twitterCardType: sharedPageData?.twitterCardType,
      twitterSite: sharedPageData?.twitterSite,
      twitterCreator: sharedPageData?.twitterCreator,
      openGraphType: sharedPageData?.openGraphType,
      param: generatePathWithTrailingSlash({
        segments: [parentSlug, childSlug],
      }),
      keywords,
    })
  }

  // Used for homepage as well as for other non-content slug pages
  const seoSlug = slug === null ? null : `${slug?.[0]}`
  const seoData = await fetchSanityData<SharedPageData>({
    query: sharedPageDataQuery,
    params: {
      slug: seoSlug as string, // Need to assert for homepage slug
    },
  })
  if (!seoData) throw new Error('Could not find any SEO page data.')
  return PageSEO({
    seoImage: seoData?.pageSeo?.seoImage?.url,
    seoTitle: seoData?.pageSeo?.pageTitle,
    seoDescription: seoData?.pageSeo?.seoDescription,
    seoImageAlt: seoData?.pageSeo?.seoImage?.altText,
    pageDomain: seoData?.pageDomain,
    siteName: seoData?.siteName,
    twitterCardType: seoData?.twitterCardType,
    twitterSite: seoData?.twitterCreator,
    twitterCreator: seoData?.twitterCreator,
    openGraphType: seoData?.openGraphType,
    param: slug?.[0],
    keywords: seoData?.pageSeo?.seoKeywords,
  })
}

/** Fetches data for content pages */
const fetchContentPageData = async ({ slug }: { slug: Array<string> }) => {
  const parentSlug = slug[0] ?? ''
  const childSlug = slug[1] ?? ''
  // Fetch page data
  const { pageData, isFeatured, pageDomain } = await getContentPageData({
    parentSlug,
    childSlug,
  })

  // Fetch more like this data
  const moreLikeThis = await fetchSanityData<Array<ContentPost>>({
    query: moreLikeThisQuery,
    params: {
      slug: `${childSlug}`,
      postType: `${pageData?._type}`,
    },
  })

  return {
    pageData,
    moreLikeThis,
    isFeatured,
    pageDomain,
  }
}

export {
  fetchContentPageData,
  fetchPageSEOData,
  fetchSanityData,
  getContentPageData,
}
