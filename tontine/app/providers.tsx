'use client'

import { AuthProvider } from '../providers/AuthProvider'
import { LanguageProvider } from '../providers/LanguageProvider'
import { ToastProvider } from '../providers/ToasterProvider'
import { WebsiteIdProvider } from '../providers/WebsiteIdProvider'
import { titilliumWebFont } from './fonts'

/**
 * Wraps every page that is generated in these components
 */
export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <div className={titilliumWebFont.className}>
      <LanguageProvider>
        <WebsiteIdProvider>
          <AuthProvider>
            <ToastProvider>{children}</ToastProvider>
          </AuthProvider>
        </WebsiteIdProvider>
      </LanguageProvider>
    </div>
  )
}
