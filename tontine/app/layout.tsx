import './globals.css'

import type { Metadata, Viewport } from 'next'
import { cookies } from 'next/headers'
import Script from 'next/script'
import { Suspense } from 'react'
import { HeatMaps } from '../components/scripts/HeatMaps'
import { ToastContainer } from '../components/ui/toast/ToastContainer'
import images from '../data-resource/images.json'
import {
  getWebsiteId,
  isLoadedWindow,
  shouldTrackWithoutConsent,
} from '../helper-functions/UtilFunctions'
import { fetchSanityData } from '../sanity-queries/query-fetch-function'
import { footerAndNavQuery } from '../sanity-queries/sanity-queries'
import { FeedbackModal } from '../sections/Feedback/FeedbackModal'
import { Footer } from '../sections/Footer/Footer'
import { NavigationMenu } from '../sections/Nav/NavigationMenu'
import { isProd } from '../serverless/keys'
import type {
  SharedPageData,
  WebsiteIdsArrayType,
} from '../types/shared-page-data.types'
import { Providers } from './providers'

export const viewport: Viewport = {
  themeColor: 'white',
}

export const metadata: Metadata = {
  icons: {
    icon: [
      { url: images.FAVICON },
      {
        url: images.FAVICON,
        media: '(prefers-color-scheme: dark)',
      },
    ],
    shortcut: [images.FAVICON],
    apple: [
      { url: images.FAVICON },
      { url: images.APPLE_TOUCH_ICON, sizes: '192x192', type: 'image/png' },
    ],
    other: [
      {
        rel: 'apple-touch-icon-precomposed',
        url: images.APPLE_TOUCH_ICON,
      },
    ],
  },
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const websiteId = await getWebsiteId(cookies)

  // Fetch nav and footer
  const sharedPageData = await fetchSanityData<
    SharedPageData & WebsiteIdsArrayType
  >({
    query: footerAndNavQuery,
    websiteIdCookie: websiteId,
  })

  const hostname = isLoadedWindow() ? window.location.hostname : ''
  const skipConsentCheck = shouldTrackWithoutConsent(hostname)

  return (
    <html lang='en'>
      <head>
        {isProd && (
          <>
            <HeatMaps />
            {!skipConsentCheck && (
              <Script
                // !!!!!!!!!!!IMPORTANT!!!!!!!!!!!!!
                // The cookiebot must be loaded with "beforeInteractive"
                // otherwise the script will be blocked by browser privacy settings!!!
                // THIS MUST BE LIKE THIS IT IS RECOMMENDED by Cookiebot support as !!!
                strategy='beforeInteractive'
                id='Cookiebot'
                src='https://consent.cookiebot.eu/uc.js?cbid=18e51121-3be3-4de4-900a-f9c2b7dfaa4d'
                data-blockingmode='auto'
                type='text/javascript'
              />
            )}
          </>
        )}
      </head>
      <body>
        <Providers>
          <ToastContainer />
          <Suspense>
            <FeedbackModal
              feedbackModalData={sharedPageData?.websiteFeedbackModal}
              hideMobile
            />
            <NavigationMenu
              sharedPageData={sharedPageData}
              feedbackData={sharedPageData?.websiteFeedbackModal}
            />
            {children}
            <Footer sharedPageData={sharedPageData} />
          </Suspense>
        </Providers>
      </body>
    </html>
  )
}
