import type { MetadataRoute } from 'next'

import { manifestQuery } from '../sanity-queries/miscellaneous-queries/miscellaneous-queries'
import { fetchSanityData } from '../sanity-queries/query-fetch-function'
import type { ManifestQueryType } from '../types/common.types'

export default async function manifest(): Promise<MetadataRoute.Manifest> {
  const { name, shortName, description, pageDomain } =
    await fetchSanityData<ManifestQueryType>({ query: manifestQuery })
  return {
    name,
    short_name: shortName,
    description,
    start_url: pageDomain,
    display: 'standalone',
    background_color: 'white',
    theme_color: '#2975BF',
    lang: 'en-US',
    orientation: 'any',
    icons: [
      {
        src: 'favicon.ico',
        sizes: '128x128',
        type: 'image/x-icon',
      },
      {
        purpose: 'maskable',
        sizes: '512x512',
        src: 'tontine_maskable.png',
        type: 'image/png',
      },
      {
        purpose: 'any',
        sizes: '512x512',
        src: 'tontine_rounded.png',
        type: 'image/png',
      },
    ],
  }
}
