import { cookies, draftMode } from 'next/headers'
import { notFound } from 'next/navigation'

import { DynamicPageContent } from '../../components/page-content/DynamicPageContent'
import { PageContent } from '../../components/page-content/PageContent'
import {
  processPages,
  writePagesData,
} from '../../helper-functions/AppUtilFunctions'
import { getWebsiteId } from '../../helper-functions/UtilFunctions'
import {
  fetchContentPageData,
  fetchPageSEOData,
  fetchSanityData,
} from '../../sanity-queries/query-fetch-function'
import {
  pageDataQuery,
  pagesDataQuery,
  shareSectionQuery,
} from '../../sanity-queries/sanity-queries'
import { logServerless } from '../../serverless/ApiUtilFunctions'
import type { PagesData, StaticPageParamData } from '../../types/pages.types'
import type { SharedSectionDataType } from '../../types/sections/section.types'

export const dynamicParams = false // Replacement for the fallback option | false = 404}

type TParams = Promise<{ slug: Array<string> }>

export async function generateStaticParams() {
  const pages = await fetchSanityData<StaticPageParamData>({
    query: pagesDataQuery,
  })

  if (process.env.WRITE_PAGE_DATA && process.env.BUILD_ENV) {
    await writePagesData({ pagesData: pages })
  }

  return processPages(pages)
}

export async function generateMetadata({ params }: { params: TParams }) {
  const { slug }: { slug: Array<string> } = await params

  try {
    const { isEnabled } = await draftMode()
    const seoData = await fetchPageSEOData({
      slug,
      preview: isEnabled,
    })
    return seoData
  } catch (error) {
    logServerless({
      message: `SEO data for page ${slug?.[0]} not found`,
      logLevel: 'error',
      error,
    })
    return {
      title: 'Not Found',
      description: 'The page you are looking for does not exist',
    }
  }
}

/**
 *Page component that renders page data dynamically from Sanity CMS
 */
async function Page({ params }: { params: TParams }) {
  const { isEnabled } = await draftMode()
  const { slug: slugArray }: { slug: Array<string> } = await params
  const paramSlug = slugArray?.[0] ?? ''

  const websiteId = await getWebsiteId(cookies)

  // Sub Content Pages
  if (slugArray?.length > 1) {
    const { isFeatured, moreLikeThis, pageData, pageDomain } =
      await fetchContentPageData({
        slug: slugArray,
      })

    const shareSectionData = await fetchSanityData<SharedSectionDataType>({
      query: shareSectionQuery,
      websiteIdCookie: websiteId,
    })

    return (
      <DynamicPageContent
        moreLikeThis={moreLikeThis}
        parentSlug={paramSlug}
        singleItemData={pageData}
        shareSectionData={shareSectionData}
        isFeatured={isFeatured}
        pageDomain={pageDomain}
      />
    )
  }

  const pageData = await fetchSanityData<PagesData>({
    query: pageDataQuery,
    params: {
      slug: paramSlug,
    },
  })

  if (!pageData && !isEnabled) {
    notFound()
  }

  // Normal Content Pages
  return (
    <PageContent
      pageDomain={pageData?.pageDomain}
      pageData={pageData?.pageSections}
      pageSlug={paramSlug}
    />
  )
}

export default Page
