import type { Rule } from 'sanity'

import { slugifyString } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

type SlugProps = {
  title?: string
  description?: string
  source?: string
  isRequired?: boolean
}

/** This function returns a schema object for a slug field that can be used to
 * create a link to a section. The slug is automatically generated from the
 * section title or a custom source field.
 */
export function slugSchemaField({
  title,
  description,
  source,
  isRequired,
}: SlugProps) {
  return {
    title: title ?? 'Section Path',
    name: 'slug',
    type: 'slug',
    description:
      description ??
      'A URL-friendly version of the section title used to create a link to the section',
    options: {
      source: source ?? 'localizedTitle.en',
      slugify: slugifyString,
    },
    validation: (rule: Rule) =>
      validationRules.slugValidation(rule, isRequired),
  }
}
