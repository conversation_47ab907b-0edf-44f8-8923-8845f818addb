import type { Rule } from 'sanity'

import { SANITY_CONSTANTS } from '../../assets/constants'
import { WrappedField } from '../../components/WrappedField'
import type { ComponentProps } from '../../types/customTypes'
import { validationRules } from '../validation'

const { SUPPORTED_LANGUAGES } = SANITY_CONSTANTS

const createLocalizedFieldType = ({
  name,
  type,
  title,
  validation,
  fieldSetWrapperClass,
  isLite,
}: {
  name: string
  type: string
  title?: string
  validation?: (rule: Rule, isRequired?: boolean) => Array<Rule> | Rule
  fieldWrapperClass?: string
  fieldSetWrapperClass?: string
  isLite?: boolean
}) => ({
  title: title || `Localized ${type?.replace(/([A-Z])/g, ' $1').toLowerCase()}`,
  name,
  type: 'object',
  components: {
    field: (props: ComponentProps) =>
      WrappedField({
        customClass: fieldSetWrapperClass ?? 'locale-fieldset',
        props,
      }),
  },
  fieldsets: [
    {
      title: 'Translations',
      name: 'translations',
      options: { collapsible: true, collapsed: true },
    },
  ],

  fields: SUPPORTED_LANGUAGES.map((lang) => ({
    title: lang.title,
    name: lang.id,
    type,
    fieldset: lang.isDefault ? null : 'translations',
    validation: (rule: Rule) => validation?.(rule, lang.isDefault),
    components: {
      //TODO: Use TranslateFieldWrapper here when AI ready
      input: (props: ComponentProps) =>
        WrappedField({
          customClass: isLite ? 'rich-text-lite' : '',
          props,
        }),
    },
  })),
})

function generateRichTextFields(fieldConfig: typeof fieldsConfig) {
  const generatedFields: {
    [key: string]: ReturnType<typeof createLocalizedFieldType>
  } = {}
  fieldConfig.forEach(({ name, validation }) => {
    const capitalized = name.charAt(0).toUpperCase() + name.slice(1)

    // Regular string version
    generatedFields[`string${capitalized}`] = createLocalizedFieldType({
      name: `string${capitalized}`,
      validation,
      type: 'string',
    })

    // Regular Rich Text version
    generatedFields[`richText${capitalized}`] = createLocalizedFieldType({
      name: `richText${capitalized}`,
      validation,
      type: 'richText',
      fieldWrapperClass: 'locale-richtext',
      fieldSetWrapperClass: 'locale--fieldset-richtext',
    })

    // Rich Text Lite version
    generatedFields[`richText${capitalized}Lite`] = createLocalizedFieldType({
      name: `richText${capitalized}Lite`,
      validation,
      type: 'richTextLite',
      fieldWrapperClass: 'locale-richtext',
      fieldSetWrapperClass: 'locale--fieldset-richtext',
      isLite: true,
    })
  })

  return generatedFields
}

const fieldsConfig = [
  { name: 'title', validation: validationRules.titleValidation },
  { name: 'subtitle', validation: validationRules.subtitleValidation },
  { name: 'longTitle', validation: validationRules.longTitleValidation },
  { name: 'longSubtitle', validation: validationRules.longSubtitleValidation },
  { name: 'midTitle', validation: validationRules.midTitleValidation },
  { name: 'midSubtitle', validation: validationRules.midSubtitleValidation },
  { name: 'shortTitle', validation: validationRules.shortTitleValidation },
  {
    name: 'shortSubtitle',
    validation: validationRules.shortSubtitleValidation,
  },
  { name: 'content', validation: validationRules.contentValidation },
  { name: 'unlimited', validation: undefined },
] as const

const textFields = generateRichTextFields(fieldsConfig)

export { fieldsConfig, textFields }
