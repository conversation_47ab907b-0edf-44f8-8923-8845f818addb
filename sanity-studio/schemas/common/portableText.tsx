import {
  BarChartIcon,
  BookIcon,
  CalendarIcon,
  ColorWheelIcon,
  CommentIcon,
  ImageIcon,
  LaunchIcon,
  ThLargeIcon,
  TrendUpwardIcon,
} from '@sanity/icons'

import { TableComponent, type TableProps } from '@sanity/table'
import { SANITY_CONSTANTS } from '../../assets/constants'
import { ColoredText } from '../../components/ColoredText'
import { LinkIconRenderer } from '../../components/LinkIconRenderer'
import { TextPreview } from '../../components/TextPreview'
import { WrappedField } from '../../components/WrappedField'
import type { ComponentProps, Doc } from '../../types/customTypes'
import { validationRules } from '../validation'

const colorOptions = [
  {
    title: 'Cerulean 🔵',
    value: '#2975bf',
  },
  { title: 'Red 🔴', value: 'red' },
  { title: 'Gold 🟡', value: 'gold' },
  { title: 'Green 🟢', value: 'green' },
  {
    title: 'Dark Grey ⚫',
    value: 'darkGrey',
  },
]

const tableBlock = [
  {
    name: 'table',
    title: 'Table',
    icon: ThLargeIcon,
    type: 'table',
    components: {
      input: (props: TableProps) => (
        <TableComponent {...props} rowType={'table'} />
      ),
      preview: () => (
        <TextPreview title={'Edit/view table'} customClass='table-preview' />
      ),
    },
  },
]

const rateOptionsUSD = SANITY_CONSTANTS.DYNAMIC_METRICS.RETURNS_USD.map(
  (item) => ({
    title: `${item} USD Return Rate`,
    value: `returns-${item}-USD`,
    icon: `USD ${item}`,
  })
)

const rateOptionsEUR = SANITY_CONSTANTS.DYNAMIC_METRICS.RETURNS_USD.map(
  (item) => ({
    title: `${item} EUR Return Rate`,
    value: `returns-${item}-EUR`,
    icon: `EUR ${item}`,
  })
)

const georgeOptions = [
  {
    title: 'George Yield USA',
    value: 'george-USA',
    icon: 'USA Yield',
    color: 'red',
  },
  {
    title: 'George Yield JPN',
    value: 'george-JPN',
    icon: 'JPN Yield',
    color: 'blue',
  },
]

// Dynamic Metrics Toolbar Blocks
const dynamicMetricsBlocks = [
  {
    name: 'georgeYield',
    type: 'object',
    icon: TrendUpwardIcon,
    fields: [
      {
        name: 'george',
        type: 'string',
        options: {
          list: georgeOptions,
          layout: 'radio',
        },
        initialValue: georgeOptions[0].value,
        components: {
          field: (props: ComponentProps) => (
            <WrappedField customClass='george-yield'>
              {props?.children}
            </WrappedField>
          ),
        },
      },
    ],
    components: {
      preview: (props: ComponentProps) => (
        <TextPreview
          title={props?.title ?? 'George Yield'}
          customClass='george-yield'
          customColor='var(--cerulean-blue)'
        />
      ),
    },
  },
  {
    name: 'returnRate',
    title: 'Return Rate',
    type: 'object',
    icon: BarChartIcon,

    fields: [
      {
        name: 'rate',
        type: 'string',
        options: {
          list: [...rateOptionsEUR, ...rateOptionsUSD],
          layout: 'radio',
        },
        initialValue: rateOptionsEUR[0].value,
        components: {
          field: (props: ComponentProps) => (
            <WrappedField customClass='return-rate'>
              {props?.children}
            </WrappedField>
          ),
        },
      },
    ],
    components: {
      preview: (props: ComponentProps) => (
        <TextPreview
          title={props?.title ?? 'Return Rate'}
          customClass='rate-return'
          customColor='var(--jade-green)'
        />
      ),
    },
  },
  {
    name: 'year',
    title: 'Year',
    type: 'object',
    icon: CalendarIcon,

    fields: [
      {
        name: 'year',
        type: 'string',
        options: {
          list: [{ title: 'Year', value: `${new Date().getFullYear()}` }],
          layout: 'radio',
        },
        initialValue: `${new Date().getFullYear()}`,
        components: {
          field: (props: ComponentProps) => (
            <WrappedField customClass='year'>{props?.children}</WrappedField>
          ),
        },
      },
    ],
    components: {
      preview: () => (
        <TextPreview
          title={`${new Date().getFullYear()}`}
          customClass='year'
          customColor='var(--warn-orange)'
        />
      ),
    },
  },
]

// All Rich Text Toolbar Blocks
const richTextCustomBlocks = {
  annotations: [
    {
      name: 'link',
      type: 'object',
      title: 'External or Internal Link',
      description:
        'For external links (ex: https://google.com), for internal (ex: /about-us/#the-team or /about-us/)',
      fields: [
        {
          name: 'url',
          validation: validationRules.urlValidation,
          type: 'string',
        },
      ],
      components: {
        annotation: (props: ComponentProps) =>
          LinkIconRenderer({
            ...props,
            customClass: 'external',
            icon: <LaunchIcon />,
            value: props?.value,
          }),
      },
    },
    {
      name: 'colorPicker',
      title: 'Color Picker',
      type: 'object',
      components: {
        annotation: ColoredText,
      },
      icon: ColorWheelIcon,
      fields: [
        {
          name: 'color',
          title: 'Color',
          type: 'string',
          options: {
            list: colorOptions,
            layout: 'radio',
          },
          components: {
            field: (props: ComponentProps) => (
              <WrappedField customClass='color-picker'>
                {props?.children}
              </WrappedField>
            ),
          },
        },
      ],
    },
    {
      name: 'tooltipWrapper',
      title: 'Tooltip',
      type: 'object',
      fields: [
        {
          name: 'type',
          title: 'Tooltip Type',
          type: 'string',
          options: {
            list: [
              { title: 'Text Tooltip', value: 'text' },
              { title: 'Glossary Tooltip', value: 'glossary' },
            ],
            layout: 'radio',
          },
        },
        {
          name: 'textTooltip',
          title: 'Text Tooltip',
          type: 'object',
          hidden: ({ parent }: { parent: Doc }) => parent?.type !== 'text',
          fields: [
            {
              name: 'tooltipText',
              type: 'richTextUnlimited',
            },
          ],
          components: {
            annotation: ColoredText,
          },
          icon: CommentIcon,
        },
        {
          name: 'glossaryTooltip',
          title: 'Glossary Tooltip',
          type: 'object',
          hidden: ({ parent }: { parent: Doc }) => parent?.type !== 'glossary',
          fields: [
            {
              name: 'reference',
              type: 'reference',
              title: 'Reference',
              to: [{ type: 'glossary' }],
              options: {
                filter: () => ({
                  // Checks if item is referenced, pretty useful
                  filter: 'count(*[references(^._id)]) > 0',
                }),
              },
            },
          ],
          icon: BookIcon,
        },
      ],
    },
  ],
  inline: [
    {
      type: 'image',
      icon: ImageIcon,
    },
    ...tableBlock,
    ...dynamicMetricsBlocks,
  ],
}

export const RichText = {
  name: 'richText',
  title: 'RichText',
  type: 'array',

  of: [
    {
      type: 'block',
      styles: [
        { title: 'Normal', value: 'normal' },
        { title: 'H1', value: 'h1' },
        { title: 'H2', value: 'h2' },
        { title: 'H3', value: 'h3' },
        { title: 'H4', value: 'h4' },
        { title: 'H5', value: 'h5' },
        { title: 'H6', value: 'h6' },
        { title: 'Quote', value: 'blockquote' },
      ],
      of: [...richTextCustomBlocks.inline],
      marks: {
        decorators: [
          { title: 'Bold', value: 'strong' },
          { title: 'Italic', value: 'em' },
          { title: 'Underline', value: 'underline' },
        ],
        annotations: [...richTextCustomBlocks.annotations],
      },
    },
  ],
}

export const RichTextLite = {
  name: 'richTextLite',
  title: 'RichTextLite',
  type: 'array',

  components: {
    input: (props: ComponentProps) =>
      WrappedField({ customClass: 'rich-text-lite', props }),
  },

  of: [
    {
      type: 'block',
      styles: [
        { title: 'Normal', value: 'normal' },
        { title: 'H1', value: 'h1' },
        { title: 'H2', value: 'h2' },
        { title: 'H3', value: 'h3' },
        { title: 'H4', value: 'h4' },
        { title: 'H5', value: 'h5' },
        { title: 'H6', value: 'h6' },
        { title: 'Quote', value: 'blockquote' },
      ],
      of: [...richTextCustomBlocks.inline],
      marks: {
        decorators: [{ title: 'Italic', value: 'em' }],
        annotations: [...richTextCustomBlocks.annotations],
      },
    },
  ],
}
