import type { CurrentUser, PortableTextBlock } from 'sanity'
import { Iframe } from 'sanity-plugin-iframe-pane'
import type { StructureBuilder } from 'sanity/structure'

import type { Doc, PreviewPrepareProps } from '../../types/customTypes'

/**
 * Flatten a Portable Text block array into a plain string,
 * including annotations and custom inline objects.
 *
 */
export const flattenRichText = (blocks: Array<PortableTextBlock>) => {
  if (!blocks || !Array.isArray(blocks)) return blocks ?? ''
  return blocks
    .filter((block) => block._type === 'block' && block?.children)
    .flatMap((block) => {
      if (Array.isArray(block?.children))
        return block?.children?.map(
          (child: {
            text: string
            rate: string
            year: string
            george: string
          }) => child?.text ?? child?.rate ?? child?.year ?? child?.george ?? ''
        )
    })
    .join('')
    .trim()
}
/** Cleans a URL by removing the protocol (http:// or https://) and the 'www.' prefix. */
export const cleanDomain = (url: string) => {
  return url?.replace(/^https?:\/\/(www\.)?/, '')
}

/**
 * Generates a slug from the input string by removing special characters,
 * converting to lowercase, and replacing spaces with hyphens.
 */
export const slugifyString = (input: Array<PortableTextBlock>) => {
  const flattenedInput = flattenRichText(input)
  const sanitizedInput = flattenedInput?.replaceAll(/[^a-zA-Z0-9\s]/g, '')
  const slug = sanitizedInput
    ?.toLowerCase()
    ?.replaceAll(/\s+/g, '-')
    ?.slice(0, 60)
  return slug?.endsWith('-') ? slug?.slice(0, -1) : slug
}

// The url that will be passed to the Iframe plugin
const iFrameUrl = ({ slug }: { slug: string }) =>
  `${process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://staging-tontine-com.netlify.app'}/api/preview?secret=PREVIEWMODE&slug=${slug}`

/**
 * Generates the editing document slug needed for the iFrameUrl
 */
export const getDocumentSlug = ({ doc }: { doc: Doc }) => {
  const typeSlugMap: Record<string, string> = {
    newsPost: 'news',
    researchPost: 'research',
    videoPost: 'videos',
    blogPost: 'blog',
  }

  const prefix = typeSlugMap[doc?._type || '']
  const slugPath = prefix
    ? `/${prefix}/${doc?.slug?.current}`
    : `/${doc?.pageSlug?.current ?? ''}`

  return iFrameUrl({ slug: slugPath })
}

/**
 * Builds the frame with the content that we are trying to show on the frame.
 * Pass the StructureBuilder, url of what you want to embed, and the title of the action button
 */
export const iframeBuilder = (S: StructureBuilder, title: string) => {
  return S.view
    .component(Iframe)
    .options({
      url: (doc: Doc) =>
        getDocumentSlug({
          doc,
        }),
      reload: {
        button: true,
      },
    })
    .title(title)
}

/**
 * Converts minutes and seconds to a string
 */
export function formatMinutesSeconds(minutes: number, seconds: number): string {
  // Add a leading zero if the number of minutes or seconds is less than 10
  const formattedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`
  const formattedSeconds = seconds < 10 ? `0${seconds}` : `${seconds}`
  return `${formattedMinutes}:${formattedSeconds}`
}

/**
 * Converts seconds to minutes and seconds
 */
export function convertTimeToMinutesSeconds(seconds: number): string | null {
  if (typeof seconds !== 'number') return null

  const minutes = Math.floor(seconds / 60) // Get the whole number of minutes
  const secondsLeft = seconds % 60 // Get the remaining seconds

  return formatMinutesSeconds(minutes, secondsLeft)
}

/**
 * Creates an object array based on the specified parameters.
 *
 * - `amount` - The number of objects to create.
 * - `propertyName` - The property name to include in each object.
 * - `arrayName` - The base name for the array.
 * - `objectName` - The base name for each object.
 */
export function createObjectArray({
  amount,
  propertyName,
  arrayName,
  objectName,
}: {
  amount: number
  objectName: string
  propertyName: string
  arrayName: string
}) {
  const objectArray = Array.from({ length: amount }, (_, index) => ({
    [`${objectName}${index}`]: `${arrayName}.${index}.${propertyName}`,
  }))

  return Object.assign({}, ...objectArray) as Record<string, string>
}

/**
 * Generates a preview object for the Link Selector schema type.
 *
 * The object contains the title of the link, a subtitle that is a path to the link, and an optional media object.
 *
 * The path is determined as follows:
 *   - If customLink is set, it is used as the path.
 *   - If pageSlug is set, it is used as the path, optionally followed by a section anchor.
 *   - If pageRef is set, the path is set to '/'.
 *   - Otherwise, the path is set to 'not defined'.
 */
export function generateLinkSelectorPreview({
  title,
  pageSlug,
  pageRef,
  sectionSlug,
  customLink,
  media,
}: PreviewPrepareProps) {
  let path = ''

  if (customLink) {
    path = customLink
  } else if (pageSlug || sectionSlug) {
    path = `${pageSlug ? `/${pageSlug}/` : ''}${pageSlug ? '' : '/'}${sectionSlug ? `#${sectionSlug}` : ''}`
  } else if (pageRef) {
    path = '/'
  } else {
    path = 'not defined'
  }

  return {
    title: Array.isArray(title) ? flattenRichText(title) : title,
    subtitle: path,
    media,
  }
}

/** Generates a preview object for a section. */
export function sectionPreview({
  title,
  subtitle,
  media,
  sectionImage,
  videoThumbnail,
}: PreviewPrepareProps) {
  const flattenedTitle = Array.isArray(title) ? flattenRichText(title) : title
  const flattenedSubtitle = Array.isArray(subtitle)
    ? flattenRichText(subtitle)
    : subtitle

  return {
    title: flattenedTitle,
    subtitle: flattenedSubtitle,
    media: media ?? sectionImage ?? videoThumbnail,
  }
}

// More flexible version with default values
export const createSectionPreview = ({
  title = 'localizedTitle.en',
  subtitle = 'slug.current',
  media = 'icon',
  videoThumbnail = 'videoThumbnail',
  sectionImage = 'sectionImage',
} = {}) => ({
  select: {
    title: title,
    subtitle: subtitle,
    media: media,
    sectionImage: sectionImage,
    videoThumbnail: videoThumbnail,
  },
  prepare: sectionPreview,
})

/**
 * Checks if a given link is an external or mailto link
 */
export function isExternalLink(link: string) {
  return (
    link?.startsWith('http://') ||
    link?.startsWith('https://') ||
    link?.includes('tel:') ||
    link?.startsWith('mailto:')
  )
}

/** Determines whether a field should be hidden based on the current user's name. */
export const showDeveloperField = ({
  currentUser,
}: {
  currentUser: Omit<CurrentUser, 'role'> | null
}) => {
  return (
    currentUser?.name !== 'Dime' &&
    currentUser?.name !== 'Ivan' &&
    currentUser?.name !== 'Milosh'
  )
}

/** Converts a Sanity image ID into a URL. */
export function convertImageIdToUrl(imageId?: string) {
  const projectId = 'hl9czw39'
  const dataset = 'production'

  if (!imageId?.startsWith('image-')) {
    throw new Error('Invalid image ID format')
  }

  // Remove the 'image-' prefix
  const assetId = imageId?.replace('image-', '')

  // Replace the last '-' before the extension with a '.'
  const lastDashIndex = assetId.lastIndexOf('-')
  const formattedAssetId = `${assetId.substring(
    0,
    lastDashIndex
  )}.${assetId.substring(lastDashIndex + 1)}`

  return `https://cdn.sanity.io/images/${projectId}/${dataset}/${formattedAssetId}`
}
