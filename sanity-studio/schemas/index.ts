import { linkWithImage } from './common/linkWIthImage'
import { textFields } from './common/localeFields'
import partner from './common/partner'
import person from './common/person'
import { RichText, RichTextLite } from './common/portableText'
import socialLink from './common/sociallink'
import { blog } from './content/blog'
import contentSection from './content/contentSections'
import { news } from './content/news'
import { research } from './content/research'
import { video } from './content/video'
import ctaCardSection from './misc/cta-card'
import feedbackModal from './misc/feedbackModal'
import glossary from './misc/glossary'
import infoBlock from './misc/infoBlock'
import navigationItem from './navigation/navigationItem'
import subMenuItem from './navigation/subMenuItem'
import { page } from './pages/page'
import SEOCustomType from './sanity-custom-types/SEOCustomType'
import buttonCustomType from './sanity-custom-types/buttonCustomType'
import carouselCustomType from './sanity-custom-types/carouselCustomType'
import customImage from './sanity-custom-types/customImage'
import imageCustomType from './sanity-custom-types/imageCustomType'
import LinkCustomType from './sanity-custom-types/linkCustomType'
import questionAndAnswer from './sanity-custom-types/questionAndAnswerType'
import questionCategory from './sanity-custom-types/questionCategoryType'
import testimonialCustomType from './sanity-custom-types/testimonialCustomType'
import aboutUsSection from './section/about-us-section'
import carouselSection from './section/carousel-section'
import companiesLogos from './section/companies-logos-section'
import contactUsSection from './section/contact-us-section'
import ctaSection from './section/cta-section'
import downloadSection from './section/download-section'
import faqSection from './section/faq-section'
import featuredSection from './section/featured-section'
import footer from './section/footer-section'
import glossarySection from './section/glossary-section'
import carouselHeroSection from './section/hero-schemas/carousel-hero-section'
import heroSection from './section/hero-schemas/hero-section'
import infoBannerSection from './section/info-banner-section'
import infoBlockSection from './section/info-block-section'
import infoHubSection from './section/info-hub/info-hub-section'
import markdownSection from './section/markdown-section'
import navigationMenu from './section/nav-menu-section'
import partnersSection from './section/partners-section'
import referralSection from './section/referral-section'
import shareSection from './section/share-section'
import socialMedia from './section/social-media-section'
import team from './section/team-section'
import testimonialSection from './section/testimonial-section'
import tontinatorSection from './section/tontinator-section'
import videosSection from './section/videos-section'
import walkthroughGrid from './section/walkthrough-schemas/walkthrough-grid'
import walkThroughPin from './section/walkthrough-schemas/walkthrough-pin'
import {
  desktopPlacementSchema,
  mobilePlacementSchema,
  walkthroughPinPlacementSchema,
} from './section/walkthrough-schemas/walkthrough-pin-placement'
import walkthroughSection from './section/walkthrough-schemas/walkthrough-section'
import { corporationSchema } from './webschemas/corporation-schema'
import { investmentOrDepositSchema } from './webschemas/investment-or-deposit'
import website from './website'

export const schemaTypes = [
  contentSection,
  news,
  blog,
  research,
  video,
  heroSection,
  carouselHeroSection,
  navigationMenu,
  navigationItem,
  subMenuItem,
  footer,
  socialLink,
  socialMedia,
  shareSection,
  imageCustomType,
  faqSection,
  questionAndAnswer,
  person,
  team,
  partner,
  partnersSection,
  page,
  featuredSection,
  tontinatorSection,
  markdownSection,
  referralSection,
  website,
  ctaSection,
  contactUsSection,
  carouselCustomType,
  carouselSection,
  glossarySection,
  glossary,
  infoBlock,
  infoBlockSection,
  infoBannerSection,
  aboutUsSection,
  buttonCustomType,
  SEOCustomType,
  LinkCustomType,
  linkWithImage,
  companiesLogos,
  customImage,
  testimonialSection,
  testimonialCustomType,
  questionCategory,
  ctaCardSection,
  feedbackModal,
  investmentOrDepositSchema,
  corporationSchema,
  walkthroughSection,
  walkThroughPin,
  walkthroughGrid,
  desktopPlacementSchema,
  mobilePlacementSchema,
  walkthroughPinPlacementSchema,
  downloadSection,
  infoHubSection,
  RichText,
  RichTextLite,
  videosSection,
  ...Object.values(textFields),
]
