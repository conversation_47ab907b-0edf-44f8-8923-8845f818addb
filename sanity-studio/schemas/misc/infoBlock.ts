import { headerSchemaFields } from '../common/headerFields'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const InfoBlock = {
  // Schema for "Info Block" that appears on info block section
  name: 'infoBlock',
  title: 'Info block',
  type: 'document',
  preview: createSectionPreview({ subtitle: 'localizedSubtitle.en' }),
  fields: [
    ...headerSchemaFields({
      title: 'Info block title',
      description: 'Title field for the info block, rendered beneath the icon.',
      subtitle: {
        showSubtitle: true,
        type: 'richTextContent',
        title: 'Info block content',
        description:
          'The main content of the info block, which provides additional information or context.',
      },
      icon: {
        showIcon: true,
        title: 'Info block icon',
        description: 'Icon that should be rendered at the on top of the title.',
        validation: validationRules.imageValidation,
      },
    }),
    {
      // Field for buttons
      name: 'infoBlockButtons',
      title: 'Buttons',
      type: 'array',
      description: `<PERSON><PERSON> with a label and a link that can redirect to an internal page or section, or to an external website such as "https://google.com"`,
      validation: null,
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'buttonCustomType',
            },
          ],
        },
      ],
    },
  ],
}

export default InfoBlock
