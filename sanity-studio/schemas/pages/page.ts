import type { Rule } from 'sanity'

import PageDisclaimer from '../../components/PageDisclaimer'
import { allSectionTypes } from '../../structure-tool/sections-structure'
import { filterExistingReferences } from '../util-functions/schemaFilters'
import { slugifyString } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const page = {
  // Schema for "Pages"
  name: 'page',
  title: 'Page',
  type: 'document',
  preview: {
    select: {
      title: 'pageSeo.pageTitle',
      subtitle: 'pageSlug.current',
      media: 'pageSeo.seoImage',
    },
  },
  groups: [
    // Groups are defined, fields have been separated based on the contents.
    {
      name: 'seoData',
      title: 'SEO Data',
    },
    {
      name: 'slug',
      title: 'Page Slug',
    },
    {
      name: 'sections',
      title: 'Page Sections',
    },
  ],
  fields: [
    {
      type: 'string',
      name: 'disclaimer',
      readOnly: true,
      components: {
        input: PageDisclaimer,
      },
    },

    {
      // Field that references Seo custom type schema
      title: 'Page SEO',
      name: 'pageSeo',
      description: 'Custom SEO settings for this page.',
      type: 'seoCustomType',
      group: 'seoData',
      validation: validationRules.isRequired,
    },
    {
      // Slug field for page SEO title
      title: 'Slug',
      name: 'pageSlug',
      type: 'slug',
      description: 'A URL-friendly version of the SEO title for the page.',
      group: 'slug',
      options: {
        source: 'pageSeo.pageTitle',
        slugify: slugifyString,
      },
      validation: (rule: Rule) => validationRules.slugValidation(rule, false),
    },

    {
      // Field for sections that appear on page
      type: 'array',
      name: 'pageSections',
      title: 'Sections',
      description: 'The different sections that appear on the page.',
      group: 'sections',
      validation: validationRules.isRequired,
      of: [
        {
          type: 'reference',
          title: 'Select a section from the list',
          options: {
            filter: filterExistingReferences,
          },
          to: allSectionTypes,
        },
      ],
    },
  ],
}

export { page }
