import { HeroVariantRadio } from '../../../components/HeroVariantRadio'
import { headerSchemaFields } from '../../common/headerFields'
import { slugSchemaField } from '../../common/slug'
import sectionMedia from '../../sanity-custom-types/sectionMediaType'
import { createSectionPreview } from '../../util-functions/utilFunctions'

const HeroSection = {
  // TODO: Hide video ability when legacy
  // Schema for "Hero" section
  name: 'heroSection',
  title: 'Hero section',
  type: 'document',
  preview: createSectionPreview(),
  groups: [
    {
      name: 'content',
      title: 'Content',
    },
    {
      name: 'subscribe',
      title: 'Subscribe field',
    },
    {
      name: 'heroSectionColors',
      title: 'Colors',
    },
  ],
  fields: [
    ...headerSchemaFields({
      title: 'Hero section title',
      options: { disableColor: true, maxHeight: '3rem' },
      description: 'The title representing the hero section.',
      subtitle: {
        showSubtitle: true,
        title: 'Hero section subtitle',
        type: 'richTextLongSubtitle',
      },
      group: 'content',
    }),
    {
      name: 'heroVariants',
      type: 'string',
      title: 'Hero section type',
      description: 'Choose a section type. Hover to see an example preview.',
      options: {
        list: [
          { title: 'Legacy', value: 'legacy' },
          { title: 'Simple', value: 'simple' },
          { title: 'Modern', value: 'modern' },
        ],
        layout: 'radio',
      },
      initialValue: 'simple',
      components: { input: HeroVariantRadio },
    },
    // Field for hero section slug
    slugSchemaField({
      title: 'Hero section path',
      description:
        'The path for the hero section that will act as anchor link. Do not add "#" symbol. (e.g. "hero-section")',
    }),
    {
      // Field for buttons
      name: 'heroSectionButtons',
      title: 'Buttons',
      group: 'content',
      type: 'array',
      description:
        'Buttons with labels and links, when clicked take the user to internal page, section or an external website',
      validation: null,
      of: [
        {
          type: 'reference',
          to: [
            {
              type: 'buttonCustomType',
            },
          ],
        },
      ],
    },
    ...sectionMedia({
      hideKey: 'heroVariants',
      hideItem: 'video',
      hideValue: 'legacy',
      hideDisclaimer: 'Adding video is not allowed in Legacy Hero section type',
    }),
  ],
}

export default HeroSection
