import { headerSchemaFields } from '../common/headerFields'
import { slugSchemaField } from '../common/slug'
import { createSectionPreview } from '../util-functions/utilFunctions'
import { validationRules } from '../validation'

const ContentPosts = {
  // Schema for "content posts" designed for publishing posts to tontine.com
  name: 'contentPost',
  type: 'document',
  preview: createSectionPreview({ media: 'postImage' }),
  fields: [
    ...headerSchemaFields({
      title: 'Post title',
      description: 'The title of the post',
      type: 'richTextLongTitleLite',
      subtitle: {
        showSubtitle: true,
        title: 'Post subtitle',
        type: 'richTextLongSubtitleLite',
        description:
          'A brief summary or subtitle that appears beneath the post title',
      },
    }),
    slugSchemaField({
      title: 'Post slug',
      description:
        'A URL-friendly version of the post title used to create a link to the post',
    }),
    {
      // Field for SEO keywords
      title: 'Keywords for SEO',
      name: 'seoKeywords',
      type: 'array',
      of: [{ type: 'string' }],
      description:
        'Keywords that are as enticing as possible to attract users on social feeds and in Google searches.',
      validation: validationRules.isRequired,
    },
    {
      // Field for image
      title: 'Post image',
      name: 'postImage',
      type: 'image',
      description: 'An image that visually represents post on the website',
      validation: validationRules.imageValidation,
      options: {
        hotspot: true,
      },
    },
    {
      // Post content,
      title: 'Post body',
      name: 'localizedBody',
      type: 'richTextUnlimited',
      description: 'The main content of the post',
    },
    {
      // Define a date of publication
      title: 'Publish date',
      name: 'publishDate',
      type: 'date',
      description: 'The date when the post was published',
      validation: validationRules.isRequired,
    },
  ],
}

export default ContentPosts
