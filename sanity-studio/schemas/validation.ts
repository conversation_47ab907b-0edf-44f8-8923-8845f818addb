import { getImageDimensions } from '@sanity/asset-utils'
import type { SanityDocument } from '@sanity/client'
import type { PortableTextBlock, Rule } from 'sanity'

import { SANITY_CONSTANTS } from '../assets/constants'
import { flattenRichText, isExternalLink } from './util-functions/utilFunctions'

const { DESKTOP_SIZE, MOBILE_SIZE } = SANITY_CONSTANTS

export const DEFAULT_VALIDATION_RULES = {
  title: {
    min: 1,
    max: 100,
    maxWarning: 70,
  },
  subtitle: {
    min: 5,
    max: 80,
    maxWarning: 60,
  },
  content: {
    max: 1000,
    maxWarning: 400,
  },

  longTitle: {
    min: 15,
    max: 100,
    minWarning: 30,
    maxWarning: 70,
  },
  longSubtitle: {
    min: 40,
    max: 150,
    minWarning: 70,
    maxWarning: 130,
  },

  midTitle: {
    min: 5,
    minWarning: 5,
    max: 55,
    maxWarning: 50,
  },
  midSubtitle: {
    min: 5,
    minWarning: 5,
    max: 115,
    maxWarning: 90,
  },

  shortTitle: {
    min: 1,
    max: 20,
  },
  shortSubtitle: {
    min: 3,
    max: 55,
  },
} as const

export const CUSTOM_VALIDATION_RULES = {
  tag: {
    max: 20,
  },
  featuredPost: {
    max: 1,
  },
  readTime: {
    min: 1,
  },
  slug: {
    min: 2,
    max: 80,
  },
} as const

type ValidatorTemplateProps = {
  rule: Rule
  isRequired?: boolean
  max?: number
  min?: number
  minWarning?: number
  maxWarning?: number
}

/** Creates an array of validation rules based on provided constraints */
const validatorTemplate = ({
  rule,
  isRequired,
  max,
  maxWarning,
}: ValidatorTemplateProps) => {
  const rules = [
    isRequired && rule.required(),
    max &&
      rule.max(max).error(`You are allowed to write up to ${max} characters`),
    maxWarning &&
      rule
        .max(maxWarning)
        .warning(
          `For better SEO, use concise content under ${maxWarning} characters`
        ),
  ].filter((item): item is Rule => Boolean(item))

  return rules.length ? rules : undefined
}

/** Creates validation rules for rich text content */
const extendedValidation = ({
  rule,
  min,
  minWarning,
  max,
  maxWarning,
  isRequired,
  ...rest
}: ValidatorTemplateProps) => {
  return [
    ...(validatorTemplate?.({
      rule,
      max,
      maxWarning,
      isRequired,
      ...rest,
    }) ?? []),
    rule.custom((richTextBlocks: Array<PortableTextBlock>) => {
      if (!Array.isArray(richTextBlocks)) return true

      const text = flattenRichText(richTextBlocks)

      const length = text?.length

      if (isRequired && !text) return 'Field cannot be empty'
      if (isRequired && min && length < min)
        return `Minimum ${min} characters required`
      if (max && length > max) return `Maximum ${max} characters allowed`
      return true
    }),
    rule
      .custom((richTextBlocks: Array<PortableTextBlock>) => {
        if (!Array.isArray(richTextBlocks)) return true

        const text = flattenRichText(richTextBlocks)

        const length = text?.length
        if (minWarning && length < minWarning) {
          return {
            message: `Recommended min ${minWarning} characters for SEO (${length}/${minWarning})`,
            severity: 'warning',
          }
        }
        if (maxWarning && length > maxWarning) {
          return {
            message: `Recommended max ${maxWarning} characters for SEO (${length}/${maxWarning})`,
            severity: 'warning',
          }
        }
        return true
      })
      .warning(),
  ]
}

export const validationRules = {
  richTextTitleValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.title,
    }),

  titleValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.title,
    }),

  subtitleValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.subtitle,
    }),
  longTitleValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.longTitle,
    }),

  longSubtitleValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.longSubtitle,
    }),
  midTitleValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.midTitle,
    }),

  midSubtitleValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.midSubtitle,
    }),
  shortTitleValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.shortTitle,
    }),

  shortSubtitleValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.shortSubtitle,
    }),
  contentValidation: (rule: Rule, isRequired?: boolean) =>
    extendedValidation({
      rule,
      isRequired,
      ...DEFAULT_VALIDATION_RULES.content,
    }),

  // ----------------------------------------------------------------------------------------------------------
  // Custom Validators
  // ----------------------------------------------------------------------------------------------------------
  twitterValidation: (rule: Rule) => [
    rule.custom((value: string) =>
      value?.includes('@') ? 'The "@" symbol is not allowed' : true
    ),
  ],
  questionTagValidation: (rule: Rule) => [
    rule.custom((value: string) => {
      if (value && value.length === 0) {
        return 'Tag cannot be empty'
      }
      return true
    }),
    rule
      .max(CUSTOM_VALIDATION_RULES.tag.max)
      .error(
        `Tag cannot be more than ${CUSTOM_VALIDATION_RULES.tag.max} characters`
      ),
  ],
  faqCategoryValidation: (rule: Rule) => [rule.required().unique()],
  intersectSectionsValidation: (rule: Rule) =>
    rule.custom((value: string) => {
      if (value && value?.length === 0) {
        return 'Tag cannot be empty'
      }
      return true
    }),
  slugValidation: (rule: Rule, isRequired = true) => [
    rule.custom((value: { current: string }) => {
      const slug = value?.current
      if (isRequired) {
        if (!slug) {
          return 'A slug is required'
        }
      } else if (!isRequired && !slug) {
        return true
      }

      if (slug !== slug.toLowerCase()) {
        return 'The slug must be written in lowercase'
      }
      if (!/^[a-z0-9-]+$/i.test(slug)) {
        return 'The slug should contain only letters, numbers, or hyphens'
      }
      if (slug.endsWith('-')) {
        return 'The slug should not end with a hyphen'
      }

      if (slug.length < CUSTOM_VALIDATION_RULES.slug.min) {
        return `A slug of at least ${CUSTOM_VALIDATION_RULES.slug.min} characters is required`
      }
      if (slug.length > CUSTOM_VALIDATION_RULES.slug.max) {
        return `You are allowed to write up to ${CUSTOM_VALIDATION_RULES.slug.max} characters`
      }

      return true
    }),
  ],

  urlValidation: (rule: Rule) =>
    rule.custom((value: string) => {
      if (!value) return 'Url cannot be empty'

      if (value.startsWith('www')) {
        return `A path should not start with 'www'`
      }

      if (value.startsWith('http:')) {
        return 'Only add https urls'
      }

      if (value && !isExternalLink(value)) {
        if (value.startsWith('/')) {
          return `A path should not start with '/'`
        }
        if (value.endsWith('/')) {
          return `A path should not end with '/'`
        }
      }

      return true
    }),

  linkSelectorInternalFieldsValidation: (rule: Rule) =>
    rule.custom((value, context) => {
      const { customLink } = context.document as SanityDocument
      if (customLink && value !== undefined) {
        return 'Cannot set both page and custom path or link. Please delete the custom path/link.'
      }
      return true
    }),
  linkSelectorCustomLinkValidation: (rule: Rule) =>
    rule.custom((value: string, context) => {
      const { allPages } = context.document as SanityDocument
      if (allPages?._ref && value !== undefined) {
        return 'Cannot set both page and custom path or link. Please delete the page reference.'
      }
      if (value && !isExternalLink(value)) {
        if (value.startsWith('/')) {
          return `A path should not start with '/'`
        }
        if (value.endsWith('/')) {
          return `A path should not end with '/'`
        }
      }
      return true
    }),

  featuredSectionMediaValidation: (rule: Rule) =>
    rule.custom((_value, context) => {
      const { featuredSectionImage, featuredSectionVideo } =
        context.document as SanityDocument
      if (featuredSectionImage && featuredSectionVideo) {
        return 'Cannot set both image and video. Please remove one.'
      }
      return true
    }),

  featuredSectionMediaThumbnailValidation: (rule: Rule) =>
    rule.custom((_value, context) => {
      const { featuredSectionVideo, featuredSectionVideoThumbnail } =
        context.document as SanityDocument
      if (featuredSectionVideo && featuredSectionVideoThumbnail === undefined) {
        return 'A thumbnail image is required when a video is added.'
      }
      return true
    }),

  imageValidation: (rule: Rule) => rule.required(),
  walkthroughImageValidation: (rule: Rule) =>
    rule.required().custom((value: { asset: { _ref: string } }) => {
      if (!value) {
        return true
      }

      const { width, height } = getImageDimensions(value.asset._ref)

      if (
        (width === DESKTOP_SIZE.width && height === DESKTOP_SIZE.height) ||
        (width === MOBILE_SIZE.width && height === MOBILE_SIZE.height)
      ) {
        return true // Image is valid
      }
      return `Image must be either ${DESKTOP_SIZE.width}x${DESKTOP_SIZE.height} (desktop) or ${MOBILE_SIZE.width}x${MOBILE_SIZE.height} (mobile)`
    }),

  // When we need to be required usually at slug
  isRequired: (rule: Rule) => rule.required(),
  // Validation for number of articles in mini blog section
  articleNumberValidation: (rule: Rule) => [
    rule
      .required()
      .min(1)
      .error('You need to have at least one article in this section.'),
    rule
      .max(3)
      .error('You can only have a maximum of three articles in this section.'),
  ],
  featuredPostValidation: (rule: Rule) =>
    rule
      .required()
      .max(CUSTOM_VALIDATION_RULES.featuredPost.max)
      .error(
        `You're only allowed to have ${CUSTOM_VALIDATION_RULES.featuredPost.max} featured post.`
      ),
  readTimeValidation: (rule: Rule) =>
    rule
      .required()
      .min(CUSTOM_VALIDATION_RULES.readTime.min)
      .error(
        `You must set a minimum of ${CUSTOM_VALIDATION_RULES.readTime.min} second for read time.`
      ),
} as const
