import FieldsetWithClass from '../components/FieldsetWithClass'
import { filterExistingReferences } from './util-functions/schemaFilters'
import { validationRules } from './validation'

const websiteContent = [
  {
    name: 'navAndFooterFields',
    type: 'string',
    fieldset: 'navAndFooter',
    components: {
      field: FieldsetWithClass,
    },
  },

  {
    type: 'reference',
    name: 'navigationMenu',
    title: 'Nav on website',
    fieldset: 'navAndFooter',
    options: {
      disableNew: true,
    },
    validation: validationRules.isRequired,
    to: [{ type: 'navigationMenu' }],
  },

  {
    type: 'reference',
    name: 'footer',
    title: 'Footer on website',
    fieldset: 'navAndFooter',
    validation: validationRules.isRequired,
    options: {
      disableNew: true,
    },
    to: [{ type: 'footer' }],
  },

  {
    name: 'pagesFields',
    type: 'string',
    fieldset: 'pages',
    components: {
      field: FieldsetWithClass,
    },
  },

  {
    type: 'reference',
    name: 'homepage',
    title: 'Homepage',
    fieldset: 'pages',
    options: {
      disableNew: true,
    },
    validation: validationRules.isRequired,
    to: [{ type: 'page' }],
  },

  {
    type: 'array',
    name: 'pagesOnWebsite',
    title: 'Pages on Website',
    group: 'websitePagesMenu',
    fieldset: 'pages',
    description: 'Pages that will be added to the website.',
    of: [
      {
        type: 'reference',
        options: {
          filter: filterExistingReferences,
          disableNew: true,
        },
        to: [{ type: 'page' }],
      },
    ],
    validation: validationRules.isRequired,
  },
]

const Website = {
  // Schema for "Website"
  name: 'website',
  title: 'Website',
  type: 'document',
  groups: [
    // Groups are defined, fields have been separated based on the contents.
    {
      name: 'seoFields',
      title: 'Website SEO',
    },
    {
      name: 'webschemaFields',
      title: 'Webschemas on Website',
    },
    {
      name: 'faviconFields',
      title: 'Favicon Image',
    },
    {
      name: 'websitePagesMenu',
      title: 'Pages on Website',
    },
    {
      name: 'websiteTitleMenu',
      title: 'Title of Website',
    },
  ],
  fieldsets: [
    {
      name: 'pages',
      options: { collapsible: false, collapsed: false },
    },
    {
      name: 'navAndFooter',
      options: { collapsible: false, collapsed: false },
    },
  ],
  fields: [
    {
      // Field for Website title
      title: 'Website title',
      name: 'websiteTitle',
      type: 'string',
      group: 'websiteTitleMenu',
      description: 'The title of the website.',
      validation: validationRules.titleValidation,
    },

    ...websiteContent,

    {
      // Favicon field for image next to website name in browser tab
      title: 'Favicon',
      name: 'websiteFavicon',
      type: 'image',
      group: 'faviconFields',
      description:
        'The favicon image that appears next to the website name in the browser tab.',
      validation: validationRules.imageValidation,
      options: {
        hotspot: true,
      },
    },

    {
      // Field for page domain
      name: 'pageDomain',
      title: 'Website domain',
      type: 'url',
      group: 'seoFields',
      description: `The domain of the website, which is used to improve SEO. (e.g. "https://www.tontineira.com")`,
      validation: validationRules.isRequired,
    },
    {
      // Field for site name
      name: 'siteName',
      title: 'Company name',
      type: 'string',
      group: 'seoFields',
      description: `The name of the company, which is used to improve SEO for the entire website. (e.g. "Tontine Trust")`,
      validation: validationRules.titleValidation,
    },
    {
      // Field for open graph type
      name: 'openGraphType',
      title: 'Open graph type',
      type: 'string',
      group: 'seoFields',
      description: `The Open Graph type for the website, which should ideally be 'product' or 'website'.`,
      validation: validationRules.isRequired,
    },
    // Twitter fields required in open graph
    {
      // Field for twitter creator in open graph
      name: 'twitterCreator',
      title: 'Twitter creator',
      type: 'string',
      group: 'seoFields',
      description: `The Twitter handle of the company's creator, which is used to improve the website's SEO and help with Open Graph data. Omit @ sign.`,
      validation: validationRules.twitterValidation,
    },
    {
      // Field for twitter site in open graph
      name: 'twitterSite',
      title: 'Twitter site',
      type: 'string',
      group: 'seoFields',
      description: `The Twitter site of the company, which is used to improve the website's SEO and help with Open Graph data. Omit @ sign.`,
      validation: validationRules.twitterValidation,
    },
    {
      // Field for twitter card type in open graph
      name: 'twitterCardType',
      title: 'Twitter card type',
      type: 'string',
      group: 'seoFields',
      options: {
        list: [
          {
            title: 'Summary Card',
            value: 'summary',
          },
          {
            title: 'Summary Card with Large Image',
            value: 'summary_large_image',
          },
          {
            title: 'App Card',
            value: 'app',
          },
          {
            title: 'Player Card',
            value: 'player',
          },
        ],
      },
      description: `The type of Twitter card to use for the Open Graph data. The most commonly used type is 'summary_large_image'.`,
      validation: validationRules.titleValidation,
    },
    {
      // Reference to a feedback modal
      title: 'Feedback Modal',
      name: 'websiteFeedbackModal',
      type: 'reference',
      to: [{ type: 'feedbackModal' }],
      description: 'Reference to a feedback modal for this website.',
    },
    {
      // Field for webschema on website
      name: 'webschemasOnWebsite',
      title: 'Webschemas on Website',
      type: 'array',
      description:
        'An array of web schemas to add to the website. These schemas provide structured data about the website that can improve SEO and provide richer results in search engines.',
      group: 'webschemaFields',
      validation: validationRules.isRequired,
      of: [
        {
          type: 'reference',
          options: {
            filter: filterExistingReferences,
          },
          to: [
            {
              type: 'investmentOrDepositSchema',
            },
            {
              type: 'corporationSchema',
            },
          ],
        },
      ],
    },
  ],
}

export default Website
