import type { Doc, PreviewPrepareProps } from '../../types/customTypes'
import { headerSchemaFields } from '../common/headerFields'
import { generateLinkSelectorPreview } from '../util-functions/utilFunctions'
import linkSelector from './linkSelector'

const SubMenuItem = {
  // Schema for "Submenu item"
  name: 'subMenuItem',
  title: 'Sub-Menu Item',
  type: 'document',
  groups: [
    {
      name: 'subMenuItemLinkData',
      title: 'Submenu item link settings',
    },
  ],
  // Preview for the sub menu item
  preview: {
    select: {
      title: 'localizedTitle.en',
      pageSlug: 'allPages.pageSlug.current',
      pageRef: 'allPages._ref',
      sectionSlug: 'allPageSections.slug.current',
      customLink: 'customLink',
      media: 'icon',
    },
    prepare(selection: PreviewPrepareProps) {
      return generateLinkSelectorPreview(selection)
    },
  },
  fields: [
    ...headerSchemaFields({
      title: 'Submenu item title',
      description: 'The title of the submenu section.',
      subtitle: {
        type: 'richTextSubtitleLite',
        showSubtitle: true,
        title: 'Sub menu item description',
        description: 'A brief description of the submenu item.',
      },
      icon: {
        showIcon: true,
        title: 'Sub menu item icon',
        description: 'An icon that represents the submenu item.',
        isRequired: true,
      },
    }),

    ...linkSelector.fields,

    {
      // Field that indicates if the sub menu item is a new item
      title: 'New',
      name: 'subMenuItemNew',
      type: 'boolean',
      initialValue: false,
      description: `When toggled "ON", a "NEW" tag will be placed under the icon`,
      readOnly: ({ document }: { document: Doc }) =>
        document?.subMenuItemComingSoon,
    },
    {
      // Field that indicates if the sub menu item is a coming soon item
      title: 'Coming soon',
      name: 'subMenuItemComingSoon',
      type: 'boolean',
      initialValue: false,
      description: `When toggled "ON", a "Coming soon" tag will be placed under the icon and will be disabled.`,
      readOnly: ({ document }: { document: Doc }) => document?.subMenuItemNew,
    },
  ],
}

export default SubMenuItem
