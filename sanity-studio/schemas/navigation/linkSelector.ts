import { sectionSchemas } from '../../structure-tool/sections-structure'
import type { Doc, FilterParams } from '../../types/customTypes'
import { filterPageSections } from '../util-functions/schemaFilters'
import { validationRules } from '../validation'

// Custom link selector with two options: internal or custom
// If internal, a reference to a page and section can be set/
// If custom, a slug or a full URL to an external website can be set
const linkSelector = {
  name: 'linkSelector',
  title: 'Link Selector',
  type: 'object',
  fields: [
    {
      name: 'linkType',
      title: 'Link type',
      description:
        'Choose whether the item should direct to a page or section within the site, or to a custom slug or external URL.',
      type: 'string',
      options: {
        list: [
          { title: 'Internal', value: 'internal' },
          { title: 'Custom', value: 'custom' },
        ],
        layout: 'radio',
        direction: 'horizontal',
      },
      initialValue: 'internal',
    },
    {
      name: 'allPages',
      title: 'Select a page',
      description:
        'Select a reference to an existing page or create a new page on the website. The item link will direct users to this page.',
      type: 'reference',
      to: [{ type: 'page' }],
      hidden: ({ document }: { document: Doc }) =>
        document?.linkType !== 'internal',
      validation: validationRules.linkSelectorInternalFieldsValidation,
    },
    {
      name: 'allPageSections',
      title: `Select a section from the chosen page in the 'Select a page' field above (OPTIONAL)`,
      description:
        'Select a reference to an existing section or create a new section on the website. The item link will direct users to this section. You must select a page first.',
      type: 'reference',
      to: sectionSchemas
        .map((section) => {
          return { type: section.schemaType }
        })
        .concat([
          {
            type: 'footer',
          },
          {
            type: 'contentSection',
          },
        ]),
      hidden: ({ document }: { document: Doc }) =>
        document?.linkType !== 'internal',
      readOnly: ({ document }: { document: Doc }) => {
        return (
          document?.allPages?._ref === undefined &&
          document.allPageSections?._ref === undefined
        )
      },
      options: {
        filter: async (params: FilterParams) => filterPageSections(params),
      },
      validation: validationRules.linkSelectorInternalFieldsValidation,
    },
    {
      name: 'customLink',
      title: 'Custom link',
      description:
        'Enter a slug or a URL to an external website (e.g. "https://google.com").',
      type: 'string',
      hidden: ({ document }: { document: Doc }) =>
        document?.linkType !== 'custom',
      validation: validationRules.linkSelectorCustomLinkValidation,
    },
    {
      name: 'customParamsButton',
      title: 'Show custom params button',
      description: 'Add query parameters to the custom link (optional).',
      initialValue: false,
      type: 'boolean',
      hidden: ({ document }: { document: Doc }) =>
        document?.linkType !== 'custom',
    },
    {
      name: 'customParams',
      title: 'Custom Params',
      description:
        'Enter any custom parameters you want to add to the URL. For example, "country=USA&invStrat=BOL". Mainly used for tontinator links.',
      type: 'string',
      hidden: ({ document }: { document: Doc }) =>
        !(
          document?.linkType === 'custom' &&
          document?.customParamsButton === true
        ),
      validation: validationRules.linkSelectorCustomLinkValidation,
    },
  ],
}

export default linkSelector
