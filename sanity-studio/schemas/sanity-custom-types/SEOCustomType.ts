import { validationRules } from '../validation'

const SEOCustomType = {
  // Schema for Seo and seo fieds that it's used on every page
  name: 'seoCustomType',
  title: 'SEO fields',
  type: 'object',
  fields: [
    {
      // Field for page title
      name: 'pageTitle',
      title: 'Page title',
      type: 'string',
      description: 'A clear title representing the page content.',
      validation: validationRules.longTitleValidation,
    },
    {
      // Field for page SEO keywords
      title: 'Keywords for SEO and social sharing',
      name: 'seoKeywords',
      type: 'array',
      of: [{ type: 'string' }],
      description:
        'Keywords that are as enticing as possible to invite users in social feeds and Google searches',
      validation: validationRules.isRequired,
    },
    {
      // Short paragraph for improving seo
      title: 'Open Graph description for SEO & social sharing',
      name: 'seoDescription',
      type: 'text',
      description:
        'Crafting a compelling Open Graph description is vital for enhancing SEO and boosting social sharing. This description entices visitors from Google and social platforms, driving higher conversion rates.',
      validation: validationRules.longSubtitleValidation,
    },
    {
      // Seo image field
      title: 'Open graph image',
      name: 'seoI<PERSON>',
      type: 'image',
      options: {
        hotspot: true,
      },
      description:
        'An image that will be used as a thumbnail when your web page is shared on social media platforms like Facebook, Twitter..',
      validation: validationRules.imageValidation,
    },
  ],
}

export default SEOCustomType
