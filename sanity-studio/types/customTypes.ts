import type { ReactElement } from 'react'
import type {
  FormPatch,
  PatchEvent,
  PortableTextBlock,
  SanityClient,
  SanityDocument,
} from 'sanity'
import type { StructureBuilder } from 'sanity/structure'

import type { SANITY_CONSTANTS } from '../assets/constants'

type BlockArrayType = Array<PortableTextBlock>

/**
 * Represents properties commonly used for schema previews.
 * - 'title': The title of the schema.
 * - 'slug': The path to the schema.
 */
type PreviewPrepareProps = {
  title: BlockArrayType
  subtitle: BlockArrayType
  pageSlug: string
  pageRef: string
  sectionSlug: string
  customLink: string
  slug: string
  media: string
  imageRef: string
} & FeaturedSectionMediaType &
  SectionMediaType

type FeaturedSectionMediaType = {
  featuredSectionImage?: string
  featuredSectionVideoThumbnail?: string
}

type StructureToolTypes = {
  Build: StructureBuilder
  context: {
    getClient: (clientOptions: {
      apiVersion: string
      perspective: string
    }) => SanityClient
  }
}

type MediaParent = {
  mediaType: string
}

/** Represents a type typically used in deskStructure to associate an icon with schemas. */
type ChildSchemaType = {
  title: string
  schemaType: string
  icon: React.ComponentType | React.ReactNode
  type?: string
}

type RefType = {
  _ref: string
}

type Asset = {
  _type: string
  _id: string
} & RefType

type Slug = {
  current: string
}

type Page = {
  _id: string
  pageSections: Array<Section>
  pageSlug?: Slug
  // Add other relevant fields if necessary
}

type Section = {
  sectionSlug?: Slug
  // Add other relevant fields if necessary
} & Asset

type WalkThroughPin = {
  walkthroughMobile: RefType
  walkthroughDesktop: RefType
  walkthroughGrid: RefType
  walkthroughGridImage: { asset: RefType }
}

type CombinedFilterSchemaArrayType = {
  mobileWalkthroughs: Array<RefType>
  desktopWalkthroughs: Array<RefType>
}

type CombinedFilterSchemasType = {
  walkthroughMobile: RefType
  walkthroughDesktop: RefType
}

type Doc = {
  linkType: string
  websiteId: string
  slug: Slug
  title?: string
  pageSlug: Slug
  allPages?: RefType
  allPageSections?: RefType
  featuredPost?: Array<RefType>
  featuredPostEU?: Array<RefType>
  featuredPostUS?: Array<RefType>
  postsArray?: Array<RefType>
  postsArrayEU?: Array<RefType>
  postsArrayUS?: Array<RefType>
  dropdown?: string
  heroSectionSubscribeToggle?: boolean
  subMenuItemNew?: boolean
  subMenuItemComingSoon?: boolean
} & WalkThroughPin &
  SectionMediaType &
  SanityDocument &
  Asset

type Parent = Array<RefType>

type SchemaUtilProps = {
  parent: Parent
  document: Doc
}

type Rule = {
  flag: string
  constraint: number
}

type Validation = {
  _rules?: Array<Rule>
}

type SchemaType = {
  options?: { list: Array<{ title: string; value: string }> }
  validation?: Array<Validation>
}

type OnChange = (patch: FormPatch | Array<FormPatch> | PatchEvent) => void

type RenderDefaultProps = {
  schemaType?: SchemaType
  value?: Array<string | number>
  onChange?: OnChange
  arrayFunctions?: React.FC<Props>
}

type ComponentProps = {
  children?: ReactElement
  title?: string
  customColor?: string
  customClass?: string

  path?: string

  icon?: ReactElement
  value?: { href?: string; color?: string }
  renderDefault?: (props: unknown) => ReactElement
  onChange?: OnChange
}

type Props = {
  schemaType?: SchemaType
  value?: Array<string | number>
  onChange: OnChange
  renderDefault: (props: RenderDefaultProps) => void
}

type FilterParams = {
  document: Doc
  getClient: ({ apiVersion }: { apiVersion: string }) => SanityClient
}

type PostType = 'blogPost' | 'researchPost' | 'videoPost' | 'newsPost'

type WalkthroughPinPlacementType = { column: number; row: number }

// Define type for the parent document context
type SectionMediaType = {
  sectionVideo?: {
    asset: Asset
  }
  sectionImage?: {
    asset: Asset
  }
  videoThumbnail?: {
    asset: Asset
  }
}

type SupportedLanguagesType =
  (typeof SANITY_CONSTANTS.SUPPORTED_LANGUAGES)[number]

export type {
  ChildSchemaType,
  CombinedFilterSchemaArrayType,
  CombinedFilterSchemasType,
  ComponentProps,
  Doc,
  FilterParams,
  MediaParent,
  Page,
  Parent,
  PostType,
  PreviewPrepareProps,
  Props,
  SchemaType,
  SchemaUtilProps,
  Section,
  SectionMediaType,
  StructureToolTypes,
  SupportedLanguagesType,
  WalkthroughPinPlacementType,
}
