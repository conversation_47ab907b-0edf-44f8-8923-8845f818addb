// import { defineMigration, create } from 'sanity/migrate'

// const from = 'value to replace from' // ex: sectionSlug
// const to = 'value to replace to' // ex: slug

// const OLD_TYPES = ['pageEU', 'pageUS']
// const NEW_TYPE = 'page'

/** Alternatively, you can migrate multiple fields at once, example:

  from: ['title1', 'title2']
  to: 'title'
  documentTypes: [ 'news', 'blog' ],

  return [at(to, set(doc[from.find((field) => doc[field]) ?? 'placeholder']))]

  export default defineMigration({
    title: 'A human-friendly description of what this content migration does',
    documentTypes: ['aDocumentType'],
    migrate: {
      document(doc, context) {
        // this will be called for every document of the matching type
      },
      node(node, path, context) {
        // this will be called for every node in every document of the matching type
      },
      object(node, path, context) {
        // this will be called for every object node in every document of the matching type
      },
      array(node, path, context) {
        // this will be called for every array node in every document of the matching type
      },
      string(node, path, context) {
        // this will be called for every string node in every document of the matching type
      },
      number(node, path, context) {
        // this will be called for every number node in every document of the matching type
      },
      boolean(node, path, context) {
        // this will be called for every boolean node in every document of the matching type
      },
      null(node, path, context) {
        // this will be called for every null node in every document of the matching type
      },
    },
  })

*/
