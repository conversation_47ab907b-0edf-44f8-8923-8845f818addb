import { Stack, Text } from '@sanity/ui'
import type React from 'react'
import { useEffect, useState } from 'react'
import type { PortableTextBlock } from 'sanity'

import { convertTimeToMinutesSeconds } from '../schemas/util-functions/utilFunctions'
import { CUSTOM_VALIDATION_RULES } from '../schemas/validation'

interface ValidationRule {
  _level: string
  _rules?: Array<{
    flag: string
    constraint: number
  }>
}

interface SchemaType {
  validation: Array<ValidationRule>
  options?: { layout: string }
}

type CustomProps = {
  name?: string
  schemaType: SchemaType
  value: { _type: string; current: string } | string | undefined
  renderDefault: (props: CustomProps) => React.ReactNode
}

const colors = {
  defaultGreenColor: '#90EE90',
  greyColor: '#979CB0',
  warningColor: '#D8931B',
  errorColor: '#F24F3E',
}

/**
 * Calculates the total character count of the given Portable Text blocks
 */
const calculateCharacterCount = (
  blocks: Array<PortableTextBlock> | undefined
) => {
  let total = 0
  if (!blocks) return 0
  for (const block of blocks) {
    if (
      block?._type === 'block' &&
      block?.children &&
      Array.isArray(block?.children)
    ) {
      for (const child of block.children) {
        if (child.text) {
          total += child?.text?.length
        }
      }
    }
  }
  return total
}

/**
 * This hook takes a validation array and returns an object with
 * the current min and max constraints for both errors and warnings, as well as
 * a boolean indicating whether there are any warnings.
 */
function useValidationConstraints(validation: Array<ValidationRule>) {
  const isWarning = validation?.some((rule) => rule._level === 'warning')

  const findConstraint = (flag: 'max' | 'min', level: 'error' | 'warning') => {
    const rule = validation?.find(
      (validation) =>
        validation._level === level &&
        validation._rules?.some((rule) => rule.flag === flag)
    )
    return rule?._rules?.find((r) => r.flag === flag)?.constraint ?? null
  }

  return {
    maxConstraint: findConstraint('max', 'error'),
    minConstraint: findConstraint('min', 'error'),
    warningMax: findConstraint('max', 'warning'),
    warningMin: findConstraint('min', 'warning'),
    isWarning,
  }
}

/**
 * Renders an input field with character counter and validation warnings
 */
export function CustomFieldInput(props: CustomProps) {
  const { schemaType, value, name, renderDefault } = props
  const { validation } = schemaType ?? {}

  const { maxConstraint, minConstraint, warningMax, warningMin, isWarning } =
    useValidationConstraints(validation)

  const showReadingTime =
    name === 'readingTime' && typeof value === 'number' && value > 0

  const [counterColor, setCounterColor] = useState(colors.defaultGreenColor)

  let characterCount = 0

  const slugCheck =
    typeof value === 'object' &&
    value !== null &&
    typeof value?.current === 'string'

  if (schemaType.options?.layout !== 'radio') {
    if (typeof value === 'string') {
      characterCount = value?.length
    } else if (slugCheck) {
      characterCount = value?.current?.length
    } else if (Array.isArray(props?.value)) {
      characterCount = calculateCharacterCount(props?.value)
    }
  }

  useEffect(() => {
    if (maxConstraint) {
      if (
        characterCount > maxConstraint ||
        (minConstraint && minConstraint > characterCount)
      ) {
        setCounterColor(colors.errorColor)
      } else if (isWarning) {
        if (
          (warningMax && characterCount > warningMax) ||
          (warningMin && characterCount < warningMin)
        ) {
          setCounterColor(colors.warningColor)
        } else {
          setCounterColor(colors.defaultGreenColor)
        }
      } else {
        setCounterColor(colors.defaultGreenColor)
      }
    }
  }, [
    characterCount,
    maxConstraint,
    minConstraint,
    warningMax,
    warningMin,
    isWarning,
  ])

  const slugCountStyling =
    characterCount > CUSTOM_VALIDATION_RULES.slug.max
      ? colors.errorColor
      : colors.defaultGreenColor

  return (
    <Stack space={3}>
      {renderDefault(props)}
      {showReadingTime && (
        <Text size={1} style={{ color: colors.greyColor }}>
          Read time on post: {convertTimeToMinutesSeconds(value)} min
        </Text>
      )}
      {characterCount > 0 && (
        <Text size={1}>
          Characters:{' '}
          {slugCheck ? (
            <>
              <span
                style={{
                  color: slugCountStyling,
                }}
              >
                {characterCount}
              </span>{' '}
              <span style={{ color: colors.greyColor }}> / </span>
              <span
                style={{
                  color: slugCountStyling,
                }}
              >
                {CUSTOM_VALIDATION_RULES.slug.max}
              </span>
            </>
          ) : (
            <span style={{ color: counterColor }}>{characterCount}</span>
          )}
          {maxConstraint && (
            <>
              <span style={{ color: colors.greyColor }}> / </span>

              <span style={{ color: counterColor }}>{maxConstraint}</span>
            </>
          )}
        </Text>
      )}
    </Stack>
  )
}
