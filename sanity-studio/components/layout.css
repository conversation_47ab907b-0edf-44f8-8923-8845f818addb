html,
body {
  margin: 0;
  padding: 0;
}

:root {
  font-size: 20px;
}

[data-ui="Spinner"] {
  width: 1.3rem !important;
  height: 1.3rem !important;
}

[data-ui="Spinner"] [data-sanity-icon="spinner"] {
  width: 1.3rem !important;
  height: 1.3rem !important;
  margin: 0 !important;
}

[data-scheme="light"][data-tone="default"] {
  --card-fg-color: #252837;
  --card-border-color: #ced2d9;
}

*:not(:disabled) + [data-scheme="light"][data-tone="default"][data-border] {
  --input-box-shadow: inset 0 0 0 1px #ced2d9 !important;
}
[data-scheme="light"] [data-ui="Select"] select {
  box-shadow: inset 0 0 0 1px #ced2d9;
}

[data-as="form"] > [data-ui="Stack"] {
  grid-gap: 1.4rem;
}

[data-as="fieldset"] > [data-ui="Box"] > [data-ui="Grid"] > [data-ui="Stack"] {
  grid-gap: 1.4rem;
}

[data-ui="Stack"] [data-as="div"] + [data-ui="Text"] span {
  font-size: 0.8rem;
  line-height: calc(19 / 13);
}

/* General fieldset styles */
fieldset:has(.custom-fieldset) {
  background-color: #6363630c;
  border-radius: 6px;
  padding: 0 1rem 1rem 1rem !important;
  border: 2px solid #4287cc59 !important;
  /* box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.199) !important; */
}

/* Remove padding and border for specific child elements */
fieldset:has(.custom-fieldset) > [data-ui="Box"] {
  padding: 0 !important;
  border: 0;
}

/* Hides fieldset title */
fieldset:has(.custom-fieldset)
  [data-ui="fieldHeaderContentBox"]:nth-last-child(1) {
  display: none;
}

/* Adjust width properties for field header content */
fieldset:has(.custom-fieldset) [data-ui="fieldHeaderContentBox"] {
  max-width: unset !important;
  min-width: 100% !important;
}

/* Changes options button styling */
fieldset:has(.custom-fieldset) [data-ui="MenuButton"] {
  border: 2px solid #85898d3d !important;
}

/* Specific styles for custom-fieldset */
fieldset:has(.custom-fieldset) [data-ui="fieldHeaderContentBox"] {
  padding-bottom: 0 !important;
}

fieldset:has(.custom-fieldset) [data-ui="Grid"]:has([data-ui="Stack"]) {
  row-gap: 0.5rem !important;
}

/* Specific styles for homepageDisclaimer */
[data-testid="field-homepageDisclaimer"] [data-ui="Card"] {
  display: none !important;
  margin: 0 !important;
}

/* Style for homepage disclaimer */
[data-testid="field-homepageDisclaimer"] [for="homepageDisclaimer"] {
  border: 1px solid #d8931b8b;
  border-radius: 6px;
  color: #ffa200;
  width: 100%;
  text-align: center;
  padding-block: 1rem 0.4rem;
  margin-block: -2rem;
}

/* Categories array input */
[data-ui="ArrayInput__content"] {
  border: 1px solid var(--card-border-color);
  border-radius: 0.1875rem;
  padding-block: 0.35rem !important;
}

[data-ui="ArrayInput__content"] > [data-ui="Card"] {
  border: 0 !important;
}

#pagesOnWebsite [data-ui="Grid"]:has([data-ui="Stack"]) {
  row-gap: 0.25rem !important;
}

div[data-ui="Stack"]:has(> .custom-fieldset) {
  display: none !important;
}

.color-picker,
.rate-picker,
.george-picker {
  border: 0 !important;
  background: var(--card-bg-color) !important;
  .easymde-dropdown-content {
    background: var(--card-bg-color) !important;
    border: 1px solid var(--card-border-color);
    border-radius: 5px;
    padding: 0 !important;

    :hover {
      background: var(--card-bg-color);
    }

    :active,
    :focus,
    :checked,
    :focus-within,
    :focus-visible {
      background: darkslategray;
    }
  }

  &.rate-picker,
  &.george-picker {
    margin-inline: 0.25rem !important;
    padding-inline: 0.5rem !important;
    border-radius: 0.5rem !important;

    background: #1a1b22 !important;

    .usd,
    .eur,
    .jpn,
    .george {
      background: rgb(172, 31, 31) !important;
      width: 100% !important;
      padding: 0.25rem !important;
      border-radius: 0 !important;
      text-align: left !important;
      opacity: 0.75 !important;

      &:hover {
        opacity: 1 !important;
      }
    }

    .jpn {
      background: #dadada !important;
      color: black !important;
    }

    .eur {
      background: #2273bd8f !important;
    }
  }

  [data-testid="field-disclaimer"] {
    [data-ui="fieldHeaderContentBox"],
    [data-ui="FieldActionsFlex"] {
      display: none !important;
      margin: 0 !important;
    }
  }

  #grid-dialog {
    z-index: 99999 !important;
    * {
      padding: 0 !important;
      margin: 0 !important;
    }

    [data-ui="DialogCard"] {
      max-width: unset;
      width: fit-content;
      > [data-ui="Card"] {
        padding-top: 0.5rem !important;
        padding-left: 0.5rem !important;
        padding-bottom: 0.5rem !important;
      }
    }
    .grid-cell:hover {
      background-color: #ff0000;
    }
    .grid-cell:active {
      background-color: #ffa600;
    }
  }
}

[data-ui="CollapseMenuButton"] {
  div[data-ui="Box"] {
    display: none !important;
  }
}

.return-rate,
.color-picker,
.george-yield {
  div[data-ui="fieldHeaderContentBox"] {
    display: none !important;
  }
  svg[data-sanity-icon="ellipsis-horizontal"] {
    display: none !important;
  }
}

.pt-text-block {
  max-width: unset !important;
}

.locale-fieldset > fieldset > div {
  box-shadow: none !important;
}

.locale-fieldset {
  [data-testid="fieldset-translations"] {
    border: 1px solid #4287cc59 !important;
    border-radius: 6px;
    background-color: #3363630a;
    padding: 0.5rem;
    div {
      border: 0 !important;
      box-shadow: none !important;
      padding-left: 0 !important;
      &:hover,
      &:active,
      &:focus-visible {
        border: 0 !important;
      }
    }
  }
  [data-testid="fieldset-translations"] [data-ui="Box"] {
    border: 0 !important;
    box-shadow: none !important;
    padding-left: 0 !important;
  }
  legend {
    button {
      display: flex;
      flex-direction: row-reverse;
      gap: 0.5rem;
      cursor: pointer;
      opacity: 0.8;
    }
  }
  [data-ui="MenuButton"] {
    border: 2px solid #85898d3d !important;
  }
  [data-ui="fieldHeaderContentBox"] {
    padding: 0.2rem !important;
  }
  fieldset {
    box-shadow: none;
    border-left-color: var(--cerulean-blue) !important;
    border-radius: 3px;
  }

  .locale-string {
    display: flex;
    width: 100%;
    gap: 0.5rem !important;

    span {
      width: 100%;
    }
  }
}

.locale-richtext {
  .translate-button {
    width: 100% !important;
    margin-top: 0.25rem;
    z-index: 2;
    &::before {
      left: 0;
      right: unset;
    }
  }
}

.translate-button {
  border: 0 !important;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #8686960c;
  padding: 0;
  width: 2.5rem;
  height: 2.25rem;
  cursor: pointer;
  transition: background-color 0.2s !important;

  position: relative;

  &::before {
    position: absolute;
    content: "Translate with AI";
    top: -30px;
    border: 1px solid #8686960c;
    background-color: #2287cccc;
    border-radius: 0.2rem;
    padding: 0.2rem;
    right: 0;
    width: max-content;
    transition: opacity 0.1s;
    opacity: 0;
    pointer-events: none;
  }

  svg {
    width: 1.25rem;
    height: 1.25rem;
    color: var(--cerulean-blue);

    background: transparent !important;
  }
  &:hover {
    background-color: #4287cc59;

    &::before {
      opacity: 1;
    }
  }
  &:active {
    background-color: #8989994c;
  }
}

.locale--fieldset-richtext {
  [data-testid="fieldset-translations"] {
    border: 1px solid #4287cc59 !important;
    border-radius: 6px;
    background-color: #3363630a;
    padding: 0.5rem;
  }

  [data-testid="fieldset-translations"] > [data-ui="Box"]:not(.locale-string) {
    border: 0 !important;
    box-shadow: none !important;
    padding-left: 0 !important;
  }

  legend {
    button {
      display: flex;
      flex-direction: row-reverse;
      gap: 0.5rem;
      cursor: pointer;
      opacity: 0.8;
    }
  }
  [data-ui="MenuButton"] {
    border: 2px solid #85898d3d !important;
  }
  [data-ui="fieldHeaderContentBox"] {
    padding: 0.2rem !important;
  }
  fieldset {
    box-shadow: none;
    border-left-color: var(--cerulean-blue) !important;
    border-radius: 3px;
  }
}

.rich-text-lite {
  height: 7rem;
  position: relative;
  overflow: hidden;
  width: 100%;
  span,
  div {
    max-height: 7rem !important;
  }
}

.table-preview {
  font-weight: unset !important;
  text-decoration: none !important;
}

/* [data-testid="document-panel-portal"] {
  position: relative;
}

[data-testid="document-panel-portal"] [data-ui="Popover"] {
margin-top: 4rem !important;
}

[data-testid="document-panel-portal"] [data-ui="Popover"]:nth-of-type(2) {
margin-top: 6.25rem !important;
}

[data-testid="document-panel-portal"] [data-ui="Popover"]:nth-of-type(3) {
margin-top: 8.5rem !important;
} */

/* MIGHT BE USEFUL FOR ADJUSTING THE HEIGHT
 [data-testid='pt-editor'][data-fullscreen='false'] {
  height: 150px;
} */
