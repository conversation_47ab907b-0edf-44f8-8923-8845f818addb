{
  // Tailwind Intellisense power-up

  // Extend scope of supported attributes with `{anything}Classes/Styling/Props etc`:
  "tailwindCSS.classAttributes": [
    "class",
    "className",
    "ngClass",
    ".*className.*",
    ".*ClassName.*",
    ".*class.*",
    ".*Styling.*",
    ".*Style.*",
    ".*Props.*",
    ".*Elements.*",
    ".overrideElements"
  ],

  // Regex black magic:
  "tailwindCSS.experimental.classRegex": [
    // tailwind-variants
    ["tv\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],

    // `cn()` function from classnames / clsx
    ["cn\\(([^)]*)\\)", "(?:\"|'|`)([^(?:\"|'|`)]*)(?:\"|'|`)"],

    // Wrap style objects in `/*tw*/` before/after comments
    ["/\\*tw\\*/ ([^]*) /\\*tw\\*/", "'([^']*)'"]
  ],
  "editor.codeActionsOnSave":{
    "source.organizeImports.biome": "explicit",
    "source.addMissingImports.ts": "explicit",
    "source.fixAll.ts": "never",
    "source.removeUnused.ts": "never",
    "source.removeUnusedImports": "never",
    "source.sortImports": "always"
  },
  "editor.defaultFormatter": "biomejs.biome",
  "[typescript]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
  "cSpell.words": [
    "Cookiebot"
  ],
  "[json]": {
    "editor.defaultFormatter": "biomejs.biome"
  },
}
