import { defineConfig } from 'cypress'

const isDev = process.env.NEXT_PUBLIC_ENVIRONMENT_TYPE === 'development'

// biome-ignore lint/style/noDefaultExport: <>
export default defineConfig({
  viewportHeight: 560,
  viewportWidth: 360,
  e2e: {
    baseUrl: 'http://localhost:3000',
    video: isDev,
    screenshotOnRunFailure: isDev,
    experimentalMemoryManagement: true,
    numTestsKeptInMemory: 10,
    specPattern: [
      'cypress/e2e/FetchSanityData.cy.ts',
      'cypress/e2e/BrokenLinks.cy.ts',
      'cypress/e2e/SeoMetadata.cy.ts',
      'cypress/e2e/WebSchema.cy.ts',
    ],
    pageLoadTimeout: 20000,
  },

  component: {
    devServer: {
      framework: 'next',
      bundler: 'webpack',
    },
  },
})
