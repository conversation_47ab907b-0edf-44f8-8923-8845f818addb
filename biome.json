{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "staging"}, "files": {"ignoreUnknown": false, "ignore": [".vscode/**/*"]}, "javascript": {"formatter": {"jsxQuoteStyle": "single", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto", "bracketSpacing": true}}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "bracketSpacing": true, "ignore": ["tontine/temp-sitemap-data/**/*", "tontine/temp-sitemap-data-backup/**/*", "**/package.json", "**/out/**/*", "**/.netlify/**/*", ".vscode/**/*", "**/dist/**/*", "**/.next/**/*", "**/.sanity/**/*", "**/coverage/**/*", "**/coverage-ts/**/*", "**/node_modules/**/*", "**/public/**/*", "**/.cache/**/*", "**/.turbo/**/*", "**/.vercel/**/*", "**/*.d.ts"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noVoid": "error", "noForEach": "off"}, "correctness": {"noUndeclaredVariables": "error", "noUnusedVariables": "error", "noUnusedImports": "error", "useArrayLiterals": "error", "useExhaustiveDependencies": "warn"}, "style": {"useNodejsImportProtocol": "off", "noParameterAssign": "warn", "useBlockStatements": "off", "noDefaultExport": "error", "noNamespace": "error", "noParameterProperties": "error"}, "suspicious": {"noConsole": "warn", "noEmptyBlockStatements": "error", "noArrayIndexKey": "off"}, "nursery": {"noNestedTernary": "error", "useSortedClasses": {"level": "warn", "options": {"attributes": ["class", "className", "classList", "ClassName", "className", "ngClass", "className", "ClassName", "class", "Styl<PERSON>", "Style", "Props", "Elements", "overrideElements"], "functions": ["clsx", "cn", "tw"]}}}, "a11y": {"noSvgWithoutTitle": "off", "useSemanticElements": "off"}}, "ignore": ["tontine/temp-sitemap-data/**/*", "tontine/temp-sitemap-data-backup/**/*", "**/node_modules/**/*", "**/out/**/*", "**/public/**/*", "**/.cache/**/*", "**/.turbo/**/*", "**/.vercel/**/*", "**/.next/**/*", "**/.netlify/**/*", "**/.sanity/**/*", "**/coverage/**/*", "**/dist/**/*", "**/coverage-ts/**/*", "**/*.d.ts", ".vscode/**/*"]}, "overrides": [{"include": ["**/*.ts", "**/*.tsx"], "javascript": {"globals": ["<PERSON><PERSON><PERSON>", "NodeJS", "React"]}, "linter": {"rules": {"style": {"useConsistentArrayType": {"level": "error", "options": {"syntax": "generic"}}, "useImportType": "error", "useNamingConvention": {"level": "error", "options": {"strictCase": false, "conventions": [{"selector": {"kind": "const", "scope": "global"}, "formats": ["CONSTANT_CASE", "camelCase", "PascalCase"]}, {"selector": {"kind": "objectLiteralMember"}, "match": "_?_?(.+)"}, {"selector": {"kind": "objectLiteralMember"}, "match": "_?(.+)"}, {"selector": {"kind": "objectLiteralMember", "scope": "any"}, "formats": ["CONSTANT_CASE", "camelCase", "PascalCase", "snake_case"]}, {"selector": {"kind": "variable", "scope": "global"}, "formats": ["camelCase"]}, {"selector": {"kind": "typeLike", "scope": "global"}, "formats": ["PascalCase"]}, {"selector": {"kind": "enumMember", "scope": "any"}, "formats": ["PascalCase", "snake_case", "camelCase"]}, {"selector": {"kind": "enum", "scope": "global"}, "formats": ["PascalCase", "snake_case", "camelCase"]}, {"selector": {"kind": "typeProperty"}, "match": "_?(.+)"}, {"selector": {"kind": "typeProperty", "scope": "any"}, "formats": ["PascalCase", "snake_case", "camelCase"]}]}}}}}}, {"include": ["**/*.tsx", "**/*.jsx"], "linter": {"rules": {"correctness": {"useExhaustiveDependencies": "warn", "useHookAtTopLevel": "error"}}}}, {"include": ["cypress/**/*"], "javascript": {"globals": ["<PERSON><PERSON><PERSON><PERSON>", "cy", "expect", "describe", "it", "mount", "context", "wrap"]}}, {"include": ["tontine/app/**/*", "sanity-studio/**/*"], "linter": {"rules": {"style": {"noDefaultExport": "off"}}}}]}